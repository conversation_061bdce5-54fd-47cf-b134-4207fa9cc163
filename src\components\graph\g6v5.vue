<template>
  <div class="g6-panel">
    <div class="toolbar">
      <label>布局模式：</label>
      <select v-model="selectedLayout" @change="applyLayout">
        <option value="radial">径向分布</option>
        <option value="tree">垂直分布</option>
        <option value="dendrogram">生态树（左右）</option>
      </select>
    </div>
    <div ref="wrapRef" class="g6-wrap"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick, computed } from "vue";
import { Graph } from "@antv/g6";

const props = defineProps({
  nodes: { type: Array, required: true },
  links: { type: Array, required: true },
  showArrows: { type: Boolean, default: true },
  arrowSize: { type: Number, default: 18 },
});

const wrapRef = ref(null);
let graph = null;
let ro = null;
let roRaf = 0;
const selectedLayout = ref("radial");

/* ---------- 颜色 ---------- */
const palette = ["#1890ff", "#52c41a", "#faad14", "#f5222d", "#722ed1", "#13c2c2", "#eb2f96", "#fa8c16"];
const colorByLabel = (label) => {
  const idx = Math.abs((label || "").split("").reduce((s, c) => s + c.charCodeAt(0), 0)) % palette.length;
  return palette[idx];
};

/* ---------- 索引 ---------- */
const nodeMap = computed(() => {
  const m = new Map();
  props.nodes.forEach((n) => m.set(String(n.id), n));
  return m;
});

/* ---------- toTreeData：把 nodes/links 转成 {id, children} ---------- */
function toTreeData() {
  const id2children = new Map();
  const inDeg = new Map();
  const usedAsChild = new Set();

  props.nodes.forEach((n) => inDeg.set(String(n.id), 0));

  const addEdge = (s, t) => {
    if (!nodeMap.value.has(s) || !nodeMap.value.has(t)) return;
    if (!id2children.has(s)) id2children.set(s, new Set());
    if (!usedAsChild.has(t)) {
      // 多父时只挂一次，保证树结构
      id2children.get(s).add(t);
      usedAsChild.add(t);
      inDeg.set(t, (inDeg.get(t) || 0) + 1);
    }
  };

  props.links.filter((e) => e.name === "隶属关系").forEach((e) => addEdge(String(e.source), String(e.target)));
  props.links.filter((e) => e.name === "参与训练").forEach((e) => addEdge(String(e.source), String(e.target)));

  // 挑根：优先“作战单位”，否则入度为 0，否则第一个
  let roots = props.nodes.filter((n) => (inDeg.get(String(n.id)) || 0) === 0);
  const unitRoot = roots.find((n) => (n.label || "").includes("作战单位"));
  const root = unitRoot || roots[0] || props.nodes[0];
  const rootId = String(root?.id || "");

  if (!rootId) return { id: "root", children: [] };

  const visited = new Set();
  const build = (id) => {
    if (visited.has(id)) return null;
    visited.add(id);
    const raw = nodeMap.value.get(id);
    const node = {
      id,
      data: { label: raw?.nodeName || id, entityId: raw?.entityId, rawLabel: raw?.label },
      children: [],
    };
    (id2children.get(id) ? Array.from(id2children.get(id)) : []).forEach((cid) => {
      const sub = build(cid);
      if (sub) node.children.push(sub);
    });
    return node;
  };

  const tree = build(rootId) || { id: rootId, children: [] };

  // 把没在树里的孤点挂为并列子节点
  const seen = new Set();
  (function dfs(n) {
    seen.add(n.id);
    n.children?.forEach(dfs);
  })(tree);
  props.nodes.forEach((n) => {
    const id = String(n.id);
    if (!seen.has(id)) {
      tree.children.push({
        id,
        data: { label: n.nodeName || id, entityId: n.entityId, rawLabel: n.label },
        children: [],
      });
    }
  });

  return tree;
}

/* ---------- 布局 ---------- */
function getLayoutConfig(kind) {
  if (kind === "tree") {
    return { type: "dendrogram", direction: "TB", nodeSep: 40, rankSep: 100 };
  }
  if (kind === "dendrogram") {
    return { type: "dendrogram", direction: "LR", nodeSep: 40, rankSep: 120 };
  }
  return { type: "radial", unitRadius: 120, preventOverlap: true };
}

/* ---------- 安全 fitView/resize ---------- */
function containerBox() {
  const el = wrapRef.value;
  if (!el) return { w: 0, h: 0 };
  const { width, height } = el.getBoundingClientRect();
  return { w: Math.floor(width), h: Math.floor(height) };
}

function safeFitView() {
  if (!graph) return;
  const nodes = graph.getAllNodesData?.() || [];
  const { w, h } = containerBox();
  if (!nodes.length || w <= 0 || h <= 0) return;
  try {
    graph.fitView?.({ padding: 24, animation: false }); // 关键：关闭动画
  } catch (e) {
    // 忽略一次偶发异常
  }
}

/* ---------- 构建 ---------- */
async function build() {
  const el = wrapRef.value;
  if (!el) return;

  // 等容器可测量
  await nextTick();
  let { w, h } = containerBox();
  if (w <= 0 || h <= 0) {
    w = 960;
    h = 600;
  } // 兜底

  graph = new Graph({
    container: el,
    width: w,
    height: h,
    data: toTreeData(),
    layout: getLayoutConfig(selectedLayout.value),
    node: {
      type: "circle",
      style: (d) => ({
        size: 34,
        fill: colorByLabel(d?.data?.rawLabel),
        stroke: "#fff",
        lineWidth: 3,
        labelText: d?.data?.label || d.id,
        labelFill: "#333",
        labelFontSize: 13,
        labelFontWeight: "bold",
        labelPlacement: "bottom",
        labelOffsetY: 10,
      }),
      state: {
        highlight: { stroke: "#ff4d4f", lineWidth: 4 },
        dark: { opacity: 0.25 },
      },
    },
    edge: {
      type: "line",
      style: () => ({
        stroke: "#8aa4c4",
        lineWidth: 2,
        opacity: 0.9,
        endArrow: props.showArrows ? { size: props.arrowSize, fill: "#8aa4c4" } : false,
      }),
      state: {
        highlight: { stroke: "#2ca02c", lineWidth: 3, opacity: 1 },
        dark: { opacity: 0.15 },
      },
    },
    behaviors: ["drag-canvas", "zoom-canvas", "drag-element"],
  });

  await graph.render(); // 先渲染
  safeFitView(); // 再安全地 fit（无动画）

  // 点击高亮父/子
  graph.on("node:click", (ev) => {
    const id = ev.itemId;
    highlightRelatives(id);
  });
  graph.on("canvas:click", () => clearHighlight());

  // ResizeObserver：raf 防抖 + 尺寸守卫
  ro = new ResizeObserver(() => {
    if (!graph || graph.destroyed) return;
    if (roRaf) cancelAnimationFrame(roRaf);
    roRaf = requestAnimationFrame(() => {
      const { w, h } = containerBox();
      if (w > 0 && h > 0) {
        graph.setSize([w, h]); // 这里只改尺寸，不立刻 fit
        safeFitView(); // 紧跟一次无动画 fit
      }
    });
  });
  ro.observe(el);
}

/* ---------- 高亮 ---------- */
function clearHighlight() {
  if (!graph) return;
  graph.getAllNodesData().forEach((n) => graph.setElementState(n.id, ["highlight", "dark"], false));
  graph.getAllEdgesData().forEach((e) => graph.setElementState(e.id, ["highlight", "dark"], false));
}

function highlightRelatives(id) {
  if (!graph) return;
  clearHighlight();
  const parent = graph.getParentData(id);
  const children = graph.getChildrenData(id) || [];
  const related = new Set([id]);
  if (parent) related.add(parent.id);
  children.forEach((c) => related.add(c.id));

  graph.getAllNodesData().forEach((n) => {
    if (!related.has(n.id)) graph.setElementState(n.id, "dark", true);
  });
  graph.setElementState(id, "highlight", true);

  graph.getAllEdgesData().forEach((e) => {
    const onPath = related.has(e.source) && related.has(e.target);
    graph.setElementState(e.id, onPath ? "highlight" : "dark", true);
  });
}

/* ---------- 切换布局 ---------- */
async function applyLayout() {
  if (!graph) return;
  clearHighlight();
  graph.setLayout(getLayoutConfig(selectedLayout.value));
  await graph.layout(); // 等布局完成
  safeFitView(); // 无动画 fit
}

/* ---------- 生命周期 ---------- */
onMounted(async () => {
  await build();
});

onBeforeUnmount(() => {
  if (ro) ro.disconnect?.();
  if (roRaf) cancelAnimationFrame(roRaf);
  graph?.destroy?.();
});

/* ---------- 数据变更 ---------- */
watch(
  () => [props.nodes, props.links],
  async () => {
    if (!graph) return;
    graph.setData(toTreeData());
    await graph.render(); // v5：更新数据后直接 render
    await applyLayout(); // 走同样的流程（无动画 fit）
  },
  { deep: true }
);
</script>

<style scoped>
.g6-panel {
  position: relative;
  width: 100%;
  height: 100%;
}
.toolbar {
  position: absolute;
  z-index: 10;
  top: 12px;
  left: 12px;
  background: rgba(255, 255, 255, 0.97);
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 8px 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 8px;
  align-items: center;
}
.toolbar select {
  border: 1px solid #d1d5db;
  background: #fff;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 13px;
  min-width: 160px;
  cursor: pointer;
}
.g6-wrap {
  width: 100%;
  height: 100%;
  min-height: 520px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}
</style>
