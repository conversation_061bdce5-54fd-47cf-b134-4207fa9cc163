<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import flvjs from "flv.js";

/** 响应式状态 */
const videoRef = ref(null);
const isConnected = ref(false);
const isStreaming = ref(false);
const connectionStatus = ref("未连接");
const logs = ref([]);

/** WebSocket和播放器实例 */
let ws = null;
let player = null;
let mediaSource = null;
let sourceBuffer = null;

/** 配置 */
const wsUrl = "ws://***********:9085/ws/video-stream";
const maxLogs = 50;

/** 添加日志 */
function addLog(message) {
  const timestamp = new Date().toLocaleTimeString();
  logs.value.unshift(`[${timestamp}] ${message}`);
  if (logs.value.length > maxLogs) {
    logs.value = logs.value.slice(0, maxLogs);
  }
  console.log(message);
}

/** 开始推流 */
function startStream() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    addLog("错误: WebSocket 尚未连接");
    return;
  }

  const startCommand = {
    action: "start",
    streamUrl: "http://devimages.apple.com.edgekey.net/streaming/examples/bipbop_4x3/gear2/prog_index.m3u8",
  };

  ws.send(JSON.stringify(startCommand));
  addLog(`发送开始指令: ${JSON.stringify(startCommand)}`);

  isStreaming.value = true;
  connectionStatus.value = "正在请求流...";
}

/** 停止推流 */
function stopStream() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    addLog("错误: WebSocket 尚未连接");
    return;
  }

  const stopCommand = { action: "stop" };
  ws.send(JSON.stringify(stopCommand));
  addLog(`发送停止指令: ${JSON.stringify(stopCommand)}`);

  destroyPlayer();
  isStreaming.value = false;
  connectionStatus.value = "已停止";
}

/** 使用MediaSource API处理FLV数据 */
function initMediaSource() {
  if (!("MediaSource" in window)) {
    addLog("错误: 浏览器不支持 MediaSource API");
    return false;
  }

  try {
    mediaSource = new MediaSource();
    const videoElement = videoRef.value;
    videoElement.src = URL.createObjectURL(mediaSource);

    mediaSource.addEventListener("sourceopen", () => {
      addLog("MediaSource 已打开");
      try {
        // 尝试不同的 MIME 类型
        const mimeTypes = [
          "video/x-flv",
          'video/mp4; codecs="avc1.42E01E,mp4a.40.2"',
          'video/webm; codecs="vp8,vorbis"',
        ];

        for (const mimeType of mimeTypes) {
          if (MediaSource.isTypeSupported(mimeType)) {
            sourceBuffer = mediaSource.addSourceBuffer(mimeType);
            addLog(`使用 MIME 类型: ${mimeType}`);
            break;
          }
        }

        if (!sourceBuffer) {
          addLog("错误: 没有找到支持的 MIME 类型");
          return;
        }

        sourceBuffer.addEventListener("updateend", () => {
          addLog("SourceBuffer 更新完成");
        });

        sourceBuffer.addEventListener("error", (e) => {
          addLog(`SourceBuffer 错误: ${e}`);
        });
      } catch (e) {
        addLog(`创建 SourceBuffer 失败: ${e.message}`);
      }
    });

    mediaSource.addEventListener("sourceended", () => {
      addLog("MediaSource 已结束");
    });

    mediaSource.addEventListener("error", (e) => {
      addLog(`MediaSource 错误: ${e}`);
    });

    return true;
  } catch (error) {
    addLog(`初始化 MediaSource 失败: ${error.message}`);
    return false;
  }
}

/** 使用flv.js处理数据 */
function initFlvPlayer(flvData) {
  if (!flvjs.isSupported()) {
    addLog("错误: 浏览器不支持 flv.js");
    return;
  }

  try {
    // 创建 Blob URL
    const blob = new Blob([flvData], { type: "video/x-flv" });
    const url = URL.createObjectURL(blob);

    if (player) {
      destroyPlayer();
    }

    player = flvjs.createPlayer({
      type: "flv",
      url: url,
      isLive: true,
      hasAudio: true,
      hasVideo: true,
      enableWorker: false,
      enableStashBuffer: false,
      stashInitialSize: 128,
    });

    player.attachMediaElement(videoRef.value);

    // 监听播放器事件
    player.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
      addLog(`FLV播放器错误: ${errorType} - ${errorDetail}`);
      console.error("FLV Error Info:", errorInfo);
    });

    player.on(flvjs.Events.LOADING_COMPLETE, () => {
      addLog("FLV 加载完成");
    });

    player.on(flvjs.Events.RECOVERED_EARLY_EOF, () => {
      addLog("FLV 流恢复");
    });

    player.on(flvjs.Events.MEDIA_INFO, (mediaInfo) => {
      addLog(`媒体信息: ${JSON.stringify(mediaInfo)}`);
    });

    player.load();

    player
      .play()
      .then(() => {
        addLog("开始播放 FLV 流");
        connectionStatus.value = "正在播放";
      })
      .catch((err) => {
        addLog(`播放失败: ${err.message}`);
        connectionStatus.value = "播放失败";
      });
  } catch (error) {
    addLog(`初始化 FLV 播放器失败: ${error.message}`);
  }
}

/** 处理接收到的FLV数据 */
function handleFlvData(data) {
  addLog(`收到 FLV 数据，长度: ${data.byteLength || data.length} 字节`);

  // 方法1: 使用 flv.js
  initFlvPlayer(data);

  // 方法2: 使用 MediaSource API (备选)
  // if (sourceBuffer && !sourceBuffer.updating) {
  //   try {
  //     sourceBuffer.appendBuffer(data);
  //   } catch (e) {
  //     addLog(`添加数据到 SourceBuffer 失败: ${e.message}`);
  //   }
  // }
}

/** 销毁播放器 */
function destroyPlayer() {
  if (player) {
    try {
      player.pause();
      player.unload();
      player.detachMediaElement();
      player.destroy();
      addLog("播放器已销毁");
    } catch (e) {
      addLog(`销毁播放器时出错: ${e.message}`);
    }
    player = null;
  }

  if (mediaSource) {
    try {
      if (sourceBuffer && mediaSource.readyState === "open") {
        mediaSource.removeSourceBuffer(sourceBuffer);
      }
      if (mediaSource.readyState === "open") {
        mediaSource.endOfStream();
      }
    } catch (e) {
      addLog(`销毁 MediaSource 时出错: ${e.message}`);
    }
    mediaSource = null;
    sourceBuffer = null;
  }

  if (videoRef.value) {
    videoRef.value.src = "";
    videoRef.value.load();
  }
}

/** 连接WebSocket */
function connectWebSocket() {
  if (ws && ws.readyState === WebSocket.OPEN) {
    return;
  }

  connectionStatus.value = "正在连接...";
  addLog(`正在连接到: ${wsUrl}`);

  ws = new WebSocket(wsUrl);
  ws.binaryType = "arraybuffer";

  ws.onopen = () => {
    addLog("WebSocket 连接成功");
    isConnected.value = true;
    connectionStatus.value = "已连接";
  };

  ws.onmessage = (event) => {
    try {
      if (typeof event.data === "string") {
        // 文本消息
        const message = JSON.parse(event.data);
        addLog(`收到文本消息: ${JSON.stringify(message)}`);

        if (message.messageType === 50) {
          addLog("检测到 messageType: 50 的 FLV 数据消息");

          if (message.data) {
            let flvData;
            if (typeof message.data === "string") {
              // Base64 解码
              const binaryString = atob(message.data);
              flvData = new Uint8Array(binaryString.length);
              for (let i = 0; i < binaryString.length; i++) {
                flvData[i] = binaryString.charCodeAt(i);
              }
            } else {
              flvData = new Uint8Array(message.data);
            }
            handleFlvData(flvData);
          }
        }
      } else {
        // 二进制数据
        addLog(`收到二进制数据: ${event.data.byteLength} 字节`);
        handleFlvData(event.data);
      }
    } catch (error) {
      addLog(`处理消息时出错: ${error.message}`);
    }
  };

  ws.onerror = (error) => {
    addLog(`WebSocket 错误: ${error}`);
    isConnected.value = false;
    connectionStatus.value = "连接错误";
  };

  ws.onclose = (event) => {
    addLog(`WebSocket 已关闭 (代码: ${event.code}, 原因: ${event.reason})`);
    isConnected.value = false;
    isStreaming.value = false;
    connectionStatus.value = "连接已关闭";
  };
}

/** 清空日志 */
function clearLogs() {
  logs.value = [];
}

/** 重新连接 */
function reconnect() {
  if (ws) {
    ws.close();
  }
  setTimeout(() => {
    connectWebSocket();
  }, 1000);
}

onMounted(() => {
  connectWebSocket();
  // 初始化 MediaSource (可选)
  // initMediaSource();
});

onBeforeUnmount(() => {
  if (isStreaming.value) {
    stopStream();
  }
  destroyPlayer();
  if (ws) {
    ws.close();
    ws = null;
  }
});
</script>

<template>
  <div class="websocket-flv-player">
    <div class="header">
      <h2>WebSocket FLV 流播放器</h2>
      <div class="status-bar">
        <span class="status-text">{{ connectionStatus }}</span>
        <span :class="['status-indicator', { connected: isConnected }]">●</span>
      </div>
    </div>

    <div class="control-panel">
      <div class="button-group">
        <button @click="startStream" :disabled="!isConnected || isStreaming" class="btn btn-start">开始推流</button>

        <button @click="stopStream" :disabled="!isConnected || !isStreaming" class="btn btn-stop">停止推流</button>

        <button @click="reconnect" :disabled="isConnected" class="btn btn-reconnect">重新连接</button>

        <button @click="clearLogs" class="btn btn-clear">清空日志</button>
      </div>
    </div>

    <div class="content">
      <div class="video-section">
        <video ref="videoRef" controls muted autoplay class="video-player">您的浏览器不支持视频播放</video>
      </div>

      <div class="log-section">
        <h3>实时日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            {{ log }}
          </div>
          <div v-if="logs.length === 0" class="log-empty">暂无日志</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.websocket-flv-player {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.header h2 {
  margin: 0;
  color: #333;
}

.status-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  font-weight: 500;
  color: #666;
}

.status-indicator {
  font-size: 12px;
  color: #ff4d4f;
  transition: color 0.3s;
}

.status-indicator.connected {
  color: #52c41a;
}

.control-panel {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
  min-width: 100px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-start {
  background: #52c41a;
  color: white;
}

.btn-start:hover:not(:disabled) {
  background: #73d13d;
}

.btn-stop {
  background: #ff4d4f;
  color: white;
}

.btn-stop:hover:not(:disabled) {
  background: #ff7875;
}

.btn-reconnect {
  background: #1890ff;
  color: white;
}

.btn-reconnect:hover:not(:disabled) {
  background: #40a9ff;
}

.btn-clear {
  background: #d9d9d9;
  color: #333;
}

.btn-clear:hover {
  background: #bfbfbf;
}

.content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.video-section {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-player {
  width: 100%;
  height: 400px;
  background: #000;
}

.log-section {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.log-section h3 {
  margin: 0;
  padding: 15px;
  background: #f0f0f0;
  border-bottom: 1px solid #e0e0e0;
  font-size: 16px;
  color: #333;
}

.log-container {
  height: 400px;
  overflow-y: auto;
  padding: 10px;
  font-family: "Courier New", monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-item {
  padding: 2px 0;
  color: #333;
  word-break: break-all;
}

.log-empty {
  color: #999;
  text-align: center;
  padding: 20px;
}

@media (max-width: 1024px) {
  .content {
    grid-template-columns: 1fr;
  }

  .log-container {
    height: 200px;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .button-group {
    justify-content: center;
  }

  .video-player {
    height: 250px;
  }
}
</style>
