<template>
  <div class="resize-test">
    <h1>拖拽调整大小测试</h1>
    
    <div class="test-container">
      <div 
        class="test-box"
        v-resize="{
          minWidth: 150,
          minHeight: 100,
          onResize: handleResize
        }"
      >
        <div class="box-content">
          <h3>测试容器</h3>
          <p>当前尺寸: {{ currentSize.width }}px × {{ currentSize.height }}px</p>
          <p>拖拽右下角、右边或下边来调整大小</p>
        </div>
      </div>
    </div>

    <div class="info">
      <h3>使用说明：</h3>
      <ul>
        <li>将鼠标移到蓝色容器的边缘，会看到调整大小的光标</li>
        <li>拖拽右下角可以同时调整宽度和高度</li>
        <li>拖拽右边缘只调整宽度</li>
        <li>拖拽下边缘只调整高度</li>
        <li>最小尺寸限制为 150px × 100px</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const currentSize = ref({
  width: 300,
  height: 200
})

const handleResize = (data) => {
  currentSize.value = {
    width: Math.round(data.width),
    height: Math.round(data.height)
  }
  console.log('调整大小:', data)
}
</script>

<style scoped>
.resize-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-container {
  margin: 30px 0;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.test-box {
  width: 300px;
  height: 200px;
  border: 2px solid #007bff;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: relative;
  margin: 20px auto;
}

.box-content {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
}

.box-content h3 {
  margin-top: 0;
  color: #007bff;
}

.box-content p {
  margin: 10px 0;
  color: #555;
  font-size: 14px;
}

.info {
  background: #e7f3ff;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.info h3 {
  margin-top: 0;
  color: #007bff;
}

.info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.info li {
  margin: 8px 0;
  color: #555;
}
</style>
