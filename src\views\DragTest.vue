<template>
  <div class="drag-test">
    <h1>拖拽功能测试</h1>
    
    <div class="test-area">
      <!-- 基础拖拽测试 -->
      <div 
        class="test-box basic-drag"
        v-resize="{
          minWidth: 150,
          minHeight: 100,
          handles: ['se'],
          draggable: true,
          onResize: onResize,
          onDrag: onDrag,
          onDragStart: onDragStart,
          onDragEnd: onDragEnd
        }"
      >
        <div class="content">
          <h3>基础拖拽测试</h3>
          <p>位置: {{ position.x }}, {{ position.y }}</p>
          <p>尺寸: {{ size.width }} × {{ size.height }}</p>
          <p>状态: {{ status }}</p>
        </div>
      </div>

      <!-- 带标题栏的拖拽测试 -->
      <div 
        class="test-box header-drag"
        v-resize="{
          minWidth: 200,
          minHeight: 150,
          handles: ['e', 's', 'se'],
          draggable: true,
          dragHandle: '.header',
          onResize: onHeaderResize,
          onDrag: onHeaderDrag
        }"
      >
        <div class="header">
          <span>📋 拖拽标题栏移动</span>
        </div>
        <div class="content">
          <h3>指定拖拽区域</h3>
          <p>位置: {{ headerPosition.x }}, {{ headerPosition.y }}</p>
          <p>尺寸: {{ headerSize.width }} × {{ headerSize.height }}</p>
          <p>只能通过标题栏拖拽</p>
        </div>
      </div>
    </div>

    <div class="controls">
      <h3>控制面板</h3>
      <button @click="resetPositions" class="btn">重置位置</button>
      <button @click="centerBoxes" class="btn">居中显示</button>
      <button @click="toggleDrag" class="btn">
        {{ dragEnabled ? '禁用' : '启用' }}拖拽
      </button>
    </div>

    <div class="info">
      <h3>测试说明：</h3>
      <ul>
        <li><strong>蓝色框</strong>：整个区域都可以拖拽，右下角可以调整大小</li>
        <li><strong>绿色框</strong>：只有标题栏可以拖拽，右边、下边、右下角可以调整大小</li>
        <li>拖拽时会显示实时位置信息</li>
        <li>调整大小时会显示实时尺寸信息</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 基础拖拽状态
const position = reactive({ x: 50, y: 100 })
const size = reactive({ width: 250, height: 180 })
const status = ref('就绪')
const dragEnabled = ref(true)

// 带标题栏的拖拽状态
const headerPosition = reactive({ x: 350, y: 100 })
const headerSize = reactive({ width: 280, height: 200 })

// 基础拖拽回调
const onResize = (data) => {
  size.width = Math.round(data.width)
  size.height = Math.round(data.height)
  status.value = `调整大小: ${data.direction}`
}

const onDrag = (data) => {
  position.x = Math.round(data.x)
  position.y = Math.round(data.y)
  status.value = `拖拽中: Δ(${data.deltaX}, ${data.deltaY})`
}

const onDragStart = () => {
  status.value = '开始拖拽'
}

const onDragEnd = () => {
  status.value = '拖拽结束'
  setTimeout(() => {
    status.value = '就绪'
  }, 1000)
}

// 带标题栏的回调
const onHeaderResize = (data) => {
  headerSize.width = Math.round(data.width)
  headerSize.height = Math.round(data.height)
}

const onHeaderDrag = (data) => {
  headerPosition.x = Math.round(data.x)
  headerPosition.y = Math.round(data.y)
}

// 控制方法
const resetPositions = () => {
  // 这里需要通过API来重置位置
  const basicBox = document.querySelector('.basic-drag')
  const headerBox = document.querySelector('.header-drag')
  
  if (basicBox && basicBox._resizeAPI) {
    basicBox._resizeAPI.setPosition(50, 100)
    position.x = 50
    position.y = 100
  }
  
  if (headerBox && headerBox._resizeAPI) {
    headerBox._resizeAPI.setPosition(350, 100)
    headerPosition.x = 350
    headerPosition.y = 100
  }
}

const centerBoxes = () => {
  const basicBox = document.querySelector('.basic-drag')
  const headerBox = document.querySelector('.header-drag')
  
  if (basicBox && basicBox._resizeAPI) {
    const centerX = (window.innerWidth - size.width) / 2 - 100
    const centerY = (window.innerHeight - size.height) / 2
    basicBox._resizeAPI.setPosition(centerX, centerY)
    position.x = centerX
    position.y = centerY
  }
  
  if (headerBox && headerBox._resizeAPI) {
    const centerX = (window.innerWidth - headerSize.width) / 2 + 100
    const centerY = (window.innerHeight - headerSize.height) / 2
    headerBox._resizeAPI.setPosition(centerX, centerY)
    headerPosition.x = centerX
    headerPosition.y = centerY
  }
}

const toggleDrag = () => {
  const basicBox = document.querySelector('.basic-drag')
  const headerBox = document.querySelector('.header-drag')
  
  if (dragEnabled.value) {
    basicBox?._resizeAPI?.disableDrag()
    headerBox?._resizeAPI?.disableDrag()
  } else {
    basicBox?._resizeAPI?.enableDrag()
    headerBox?._resizeAPI?.enableDrag()
  }
  
  dragEnabled.value = !dragEnabled.value
}
</script>

<style scoped>
.drag-test {
  padding: 20px;
  min-height: 100vh;
  background: #f8f9fa;
}

.test-area {
  position: relative;
  height: 500px;
  background: white;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  margin: 20px 0;
}

.test-box {
  position: absolute;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  background: white;
}

.basic-drag {
  left: 50px;
  top: 100px;
  width: 250px;
  height: 180px;
  border: 2px solid #007bff;
  cursor: move;
}

.header-drag {
  left: 350px;
  top: 100px;
  width: 280px;
  height: 200px;
  border: 2px solid #28a745;
}

.header {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 10px 15px;
  border-radius: 6px 6px 0 0;
  cursor: move;
  user-select: none;
  font-weight: 500;
}

.content {
  padding: 15px;
}

.content h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

.content p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

.controls {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin: 20px 0;
}

.controls h3 {
  margin-top: 0;
  color: #333;
}

.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn:hover {
  background: #0056b3;
}

.info {
  background: #e7f3ff;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.info h3 {
  margin-top: 0;
  color: #007bff;
}

.info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.info li {
  margin: 8px 0;
  color: #555;
}

/* 自定义手柄样式 */
:deep(.resize-handle) {
  transition: background-color 0.2s ease;
}

:deep(.resize-handle:hover) {
  background-color: rgba(0, 123, 255, 0.6) !important;
}
</style>
