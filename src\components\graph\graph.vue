<template>
  <div class="wrap">
    <div id="graph-container" class="graph"></div>

    <div class="toolbar">
      <el-button @click="switchLayout('tree')">垂直布局</el-button>
      <el-button @click="switchLayout('dendrogram')">生态树布局</el-button>
      <el-button @click="switchLayout('radial')">生态图布局</el-button>
    </div>

    <!-- 可选信息面板：点击节点后出现 -->
    <div v-if="panel.visible" class="info-panel">
      <el-card>
        <div class="panel-hd">
          <div class="title">{{ panel.title }}</div>
          <div class="sub">{{ panel.sub }}</div>
          <el-button class="close" @click="panel.visible = false">×</el-button>
        </div>
        <div class="panel-bd">
          <div class="kv" v-for="(r, i) in panel.attrs" :key="i">
            <div class="k">{{ r.name ?? r.code }}</div>
            <div class="v">{{ r.value }}</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, reactive, watch } from "vue";
import { Graph, treeToGraphData } from "@antv/g6";

/** ========== Props & 本地拷贝（保持响应式） ========== */
const props = defineProps({
  data: { type: Object, default: () => ({}) },
});
const data = reactive(props.data);

/** ========== 信息面板（可选） ========== */
const panel = reactive({
  visible: false,
  title: "",
  sub: "",
  attrs: [],
});

/** ========== 颜色配置（固定+自动补齐） ========== */
const COLOR_BY_TYPE = {
  unit: { fill: "#FFC857", stroke: "#B8860B" },
  person: { fill: "#4F8DF7", stroke: "#1F5CC4" },
  train: { fill: "#33C08D", stroke: "#1E8A62" },
  other: { fill: "#a9a9a9", stroke: "#6e6e6e" },
};
const PALETTE = [
  { fill: "#FFC857", stroke: "#B8860B" },
  { fill: "#4F8DF7", stroke: "#1F5CC4" },
  { fill: "#33C08D", stroke: "#1E8A62" },
  { fill: "#A47CF3", stroke: "#6A4FB6" },
  { fill: "#FF7A90", stroke: "#C94A5A" },
  { fill: "#8BD3E6", stroke: "#4E98A8" },
];
const colorAuto = ref({});

function computeColorAuto(gData) {
  const types = Array.from(new Set((gData.nodes || []).map((n) => n.data?.type || "other")));
  const map = {};
  let i = 0;
  for (const t of types) map[t] = COLOR_BY_TYPE[t] || PALETTE[i++ % PALETTE.length];
  return map;
}

/** ========== 链路转森林（通用） ========== */
function buildForestFromLinks(raw, options = {}) {
  const { reverseByRelation = new Set(), maxDepth = 16 } = options;
  const nodes = raw?.nodes || [];
  const links = raw?.links || [];
  const nodeById = new Map(nodes.map((n) => [String(n.id), n]));
  const getTypeKey = (n) => n?.labels?.[0] || n?.label || "other";
  const getAttr = (n, code) => n?.attrs?.find((a) => a.code === code || a.name === code)?.value ?? "";
  const getName = (n) => n?.nodeName || getAttr(n, "name") || n?.entityId || n?.id || "";
  const makeId = (type, id, ctx) => (ctx ? `${type}:${id}@${ctx}` : `${type}:${id}`);
  const toTreeNode = (n, type, ctx, parentRelation) => ({
    id: makeId(type, String(n.id), ctx),
    name: getName(n),
    collapsed: false,
    data: {
      originId: String(n.id),
      entityId: n.entityId,
      labels: n.labels,
      type,
      attrs: n.attrs,
      parentRelation: parentRelation || "",
    },
  });

  const out = new Map();
  const indeg = new Map();
  for (const e of links) {
    const rel = e.name || "";
    let s = String(e.source);
    let t = String(e.target);
    if (reverseByRelation.has(rel)) [s, t] = [t, s];
    if (!out.has(s)) out.set(s, []);
    out.get(s).push({ to: t, relation: rel });
    indeg.set(t, (indeg.get(t) || 0) + 1);
  }

  const allIds = nodes.map((n) => String(n.id));
  const rootIds = allIds.filter((id) => !indeg.get(id));

  function dfs(curId, ctx, depth, stack) {
    const n = nodeById.get(curId);
    if (!n) return null;
    const type = getTypeKey(n);
    const node = toTreeNode(n, type, ctx, stack?.parentRelation);
    if (depth >= maxDepth) return node;

    const seen = stack?.set || new Set();
    if (seen.has(curId)) return node; // 环，截断

    const nextSeen = new Set(seen);
    nextSeen.add(curId);
    const children = [];
    for (const e of out.get(curId) || []) {
      const child = dfs(e.to, `p:${curId}`, depth + 1, { set: nextSeen, parentRelation: e.relation });
      if (child) children.push(child);
    }
    if (children.length) node.children = children;
    return node;
  }

  if (rootIds.length) {
    const forest = [];
    for (const rid of rootIds) {
      const sub = dfs(rid, "", 0, { set: new Set(), parentRelation: "" });
      if (sub) forest.push(sub);
    }
    return forest;
  }

  // 无根（全环）→ 虚拟根
  const virtualChildren = [];
  for (const id of allIds) {
    const sub = dfs(id, "root", 0, { set: new Set(), parentRelation: "" });
    if (sub) virtualChildren.push(sub);
  }
  return [{ id: "ROOT", name: "ROOT", data: { type: "other" }, children: virtualChildren }];
}

/** ========== 边样式 & 辅助 ========== */
const EDGE_STYLE = {
  endArrow: true,
  labelText: (d) => d?.data?.relation ?? "",
  labelPlacement: "center",
  labelAutoRotate: false,
  labelBackground: true,
  labelPadding: [2, 6, 2, 6],
};
function isLeafNode(d) {
  return !d?.children || d.children.length === 0;
}

/** ========== 构造 gData（复用：mounted/watch） ========== */
function makeGraphData(raw) {
  const forests = buildForestFromLinks(raw);
  const treeRoot = forests.length === 1 ? forests[0] : { id: "ROOT", name: "ROOT", children: forests };
  const gData = treeToGraphData(treeRoot);

  // 将父边的 relation 回填到每条边
  const nodeIndex = new Map((gData.nodes || []).map((n) => [String(n.id), n]));
  gData.edges = (gData.edges || []).map((e) => {
    const relation = nodeIndex.get(String(e.target))?.data?.parentRelation || "";
    return { ...e, data: { ...(e.data || {}), relation } };
  });
  return gData;
}

/** ========== 图实例 & 布局切换 ========== */
const graphRef = ref(null);

const switchLayout = (type) => {
  const g = graphRef.value;
  if (!g) return;
  if (type === "tree") {
    g.setLayout({ type: "dendrogram", direction: "TB", nodeSep: 150, rankSep: 200 });
    g.setEdge({ type: "cubic-vertical", style: EDGE_STYLE, animation: { enter: false } });
  } else if (type === "dendrogram") {
    g.setLayout({ type: "dendrogram", direction: "LR", nodeSep: 130, rankSep: 200 });
    g.setEdge({ type: "cubic-horizontal", style: EDGE_STYLE, animation: { enter: false } });
  } else if (type === "radial") {
    g.setLayout({ type: "dendrogram", radial: true, nodeSep: 130, rankSep: 200 });
    g.setEdge({ type: "line", style: EDGE_STYLE, animation: { enter: false } });
  }
  g.layout();
  g.fitView(24);
};

/** ========== 轻量签名：检测数据是否真的变化 ========== */
function makeDataSignature(d) {
  const nodes = d?.nodes ?? [];
  const links = d?.links ?? [];
  let h = 0x811c9dc5; // FNV-1a 32-bit
  const mix = (s) => {
    for (let i = 0; i < s.length; i++) {
      h ^= s.charCodeAt(i);
      h = (h >>> 0) * 16777619;
    }
  };
  mix(String(nodes.length));
  for (const n of nodes) mix(`${n.id}|${n.updatedAt ?? n.version ?? ""}`);
  mix(String(links.length));
  for (const e of links) mix(`${e.source}->${e.target}|${e.name ?? ""}`);
  return h >>> 0;
}
const lastSig = ref(null);

/** ========== 将 props.data 应用到图上 ========== */
// ✅ 统一封装，兼容 v4/v5
async function updateGraphFromProps(raw) {
  const g = graphRef.value;
  if (!g) return;
  const gData = makeGraphData(raw);
  colorAuto.value = computeColorAuto(gData);

  if (typeof g.changeData === "function") {
    // G6 v4.x
    g.changeData(gData);
    g.layout();
    g.fitView(24);
  } else {
    // G6 v5.x
    g.setData(gData);
    await g.render(); // v5 需要 render 才会应用数据+执行布局
    g.fitView(24);
  }
}

/** ========== 首次创建与渲染 ========== */
onMounted(() => {
  const gData = makeGraphData(data);
  colorAuto.value = computeColorAuto(gData);

  const g = (graphRef.value = new Graph({
    container: "graph-container",
    autoFit: "view",
    data: gData,
    node: {
      type: "rect",
      style: (d) => {
        const t = d?.data?.type ?? "other";
        const { fill, stroke } = colorAuto.value[t] || COLOR_BY_TYPE.other;
        return {
          fill,
          stroke,
          lineWidth: 1.5,
          radius: 20,
          padding: [4, 8, 4, 8],
          labelText: `${d.name ?? d.id}`,
          labelFill: "#fff",
          labelPlacement: isLeafNode(d) ? "right" : "left",
          labelBackground: true,
          draggable: false,
        };
      },
      animation: { enter: false },
    },
    edge: { type: "cubic-horizontal", style: EDGE_STYLE, animation: { enter: false } },
    layout: { type: "dendrogram", direction: "LR", nodeSep: 130, rankSep: 200 },
    behaviors: [
      { type: "drag-canvas", enable: true },
      { type: "zoom-canvas", enable: true },
      { type: "collapse-expand", trigger: "click", shouldBegin: (e) => e?.targetType === "node" },
    ],
    plugins: [
      {
        type: "tooltip",
        key: "node-tooltip",
        itemTypes: ["node"],
        trigger: "hover",
        getContent: (e) => {
          if (e.targetType !== "node") return "";
          const nodeId = e?.target?.id;
          if (!nodeId) return "";
          const nodeData = g.getNodeData(nodeId);
          const attrs = nodeData?.data?.attrs || [];
          const rows = attrs.map((a) => `<div class="row"><b>${a.name ?? a.code}</b>：${a.value ?? ""}</div>`).join("");
          return `<div class="g6-tip"><div class="hd">${nodeData?.name ?? nodeId}</div>${
            rows || "<i>无属性</i>"
          }</div>`;
        },
        shouldBegin: (e) => e?.target?.type === "node",
        offsetX: 12,
        offsetY: 8,
      },
      { type: "fullscreen", key: "fullscreen" },
      { type: "minimap", size: [240, 160] },
    ],
    theme: "dark",
  }));

  g.render();

  // 记录初始签名
  lastSig.value = makeDataSignature(props.data || {});
});

/** ========== 资源释放 ========== */
onBeforeUnmount(() => {
  graphRef.value?.destroy();
  graphRef.value = null;
});

/** ========== 监听 props.data，变化时重渲染 ========== */
watch(
  () => props.data,
  (raw) => {
    const sig = makeDataSignature(raw || {});
    if (sig === lastSig.value) return; // 没变就不动
    lastSig.value = sig;
    updateGraphFromProps(raw || {});
  },
  { deep: true, flush: "post" }
);
</script>

<style scoped>
.wrap {
  position: relative;
}
.toolbar {
  display: flex;
  gap: 8px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}
.graph {
  width: 100%;
  height: 800px;
  cursor: grab;
}
.graph:active {
  cursor: grabbing;
}

/* 信息面板（可选） */
.info-panel {
  position: absolute;
  right: 12px;
  top: 64px;
  width: 340px;
  max-height: calc(100% - 96px);
  background: #111;
  color: #fff;
  border: 1px solid #333;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.35);
}
.panel-hd {
  display: flex;
  align-items: start;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid #222;
}
.title {
  font-weight: 700;
  font-size: 16px;
  flex: 1;
}
.sub {
  color: #aaa;
  font-size: 12px;
  margin-top: 2px;
}
.close {
  background: transparent;
  border: none;
  color: #aaa;
  font-size: 20px;
  cursor: pointer;
}
.panel-bd {
  padding: 10px 12px;
  max-height: 520px;
  overflow: auto;
}
.kv {
  display: grid;
  grid-template-columns: 110px 1fr;
  gap: 6px 10px;
  padding: 6px 0;
  border-bottom: 1px dashed #222;
}
.k {
  color: #9ecbff;
  word-break: break-all;
}
.v {
  color: #e8e8e8;
  word-break: break-all;
}

/* G6 Tooltip */
.g6-tip {
  max-width: 320px;
  background: rgba(0, 0, 0, 0.9);
  color: #fff;
  border: 1px solid #333;
  padding: 8px 10px;
  border-radius: 6px;
}
.g6-tip .hd {
  font-weight: 700;
  margin-bottom: 6px;
}
.g6-tip .row {
  font-size: 12px;
  margin: 2px 0;
}
</style>
