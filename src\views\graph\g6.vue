<template>
  <div>
    <div id="container"></div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { Graph, treeToGraphData } from "@antv/g6";
import { fetchGraphData } from "@/api";

let graph = null;
const mockData = ref(null);

const getRandomColor = () => {
  const r = Math.floor(Math.random() * 256)
    .toString(16)
    .padStart(2, "0");
  const g = Math.floor(Math.random() * 256)
    .toString(16)
    .padStart(2, "0");
  const b = Math.floor(Math.random() * 256)
    .toString(16)
    .padStart(2, "0");
  return `#${r}${g}${b}`;
};

const getData = async () => {
  const res = await fetchGraphData();
  mockData.value = res.data;
  // 为每个节点添加颜色属性
  const addColor = (node) => {
    // 你可以用任意逻辑生成颜色，下面示例是随机颜色
    node.style = {
      fill: getRandomColor(),
    };
    if (node.children) {
      node.children.forEach(addColor);
    }
  };

  addColor(mockData.value); // 为整个数据结构的每个节点添加颜色属性
  console.log(treeToGraphData(mockData.value));
  console.log(mockData.value);
};

onMounted(async () => {
  await getData();
  graph = new Graph({
    container: document.getElementById("container"),
    autoFit: "view",
    data: treeToGraphData(mockData.value),
    modes: {
      default: ["drag-canvas", "zoom-canvas", "drag-element"],
    },
    node: {
      style: {
        labelText: (d) => d.id,
        // labelBackground: true,
      },
      state: {
        selected: {
          stroke: "#ff0000",
          lineWidth: 3,
        },
      },
      animation: {
        enter: false,
      },
    },
    edge: {
      state: {
        selected: {
          stroke: "#ff0000",
          lineWidth: 3,
        },
      },
    },
    layout: {
      type: "dendrogram",
      radial: true,
      nodeSep: 40,
      rankSep: 140,
      gpuEnabled: true,
    },
  });
  graph.render();

  // 为节点添加点击事件 - G6 v5 API
  graph.on("node:click", (evt) => {
    const { target } = evt;
    const nodeId = target.id;
    const nodeData = graph.getNodeData(nodeId); // G6 v5 API
    console.log("节点被点击了：", nodeData);

    // 清除之前的选中状态
    graph.getNodesData().forEach((node) => {
      graph.setElementState(node.id, "selected", false);
    });

    // 设置当前节点为选中状态 - G6 v5 API
    graph.setElementState(nodeId, "selected", true);
  });

  // 为边添加点击事件 - G6 v5 API
  graph.on("edge:click", (evt) => {
    const { target } = evt;
    const edgeId = target.id;
    const edgeData = graph.getEdgeData(edgeId); // G6 v5 API
    console.log("边被点击了：", edgeData);

    // 清除之前的选中状态
    graph.getEdgesData().forEach((edge) => {
      graph.setElementState(edge.id, "selected", false);
    });

    // 设置当前边为选中状态 - G6 v5 API
    graph.setElementState(edgeId, "selected", true);
  });
});
</script>

<style scoped>
#container {
  width: 100%;
  height: calc(100vh - 48px);
}
</style>
