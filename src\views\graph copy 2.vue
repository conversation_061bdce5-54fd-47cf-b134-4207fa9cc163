<template>
  <div class="wrap">
    <div id="graph-container" class="graph"></div>

    <div class="toolbar">
      <button @click="switchLayout('tree')">垂直布局</button>
      <button @click="switchLayout('dendrogram')">生态树布局</button>
      <button @click="switchLayout('radial')">生态图布局</button>
    </div>
    <!-- 右上角行动信息面板 -->
    <div class="action-panel">
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="行动信息" name="action">
          <div class="action-info">
            <div class="info-item" v-for="item in actionInfo" :key="item.key">
              <span class="label">{{ item.label }}：</span>
              <span class="value">{{ item.value || "暂无" }}</span>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 信息面板：点击节点后出现 -->
    <div v-if="panel.visible" class="info-panel">
      <div class="panel-hd">
        <div class="title">{{ panel.title }}</div>
        <div class="sub">{{ panel.sub }}</div>
        <button class="close" @click="panel.visible = false">×</button>
      </div>
      <div class="panel-bd">
        <div class="kv" v-for="(r, i) in panel.attrs" :key="i">
          <div class="k">{{ r.name ?? r.code }}</div>
          <div class="v">{{ r.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, reactive } from "vue";
import { Graph, treeToGraphData } from "@antv/g6";

/* ================== 数据：用你给的 data 原样放这儿 ================== */
// 假设这是你请求返回的数据 data.details
const data = {
  nodes: [
    {
      id: "0",
      nodeId: 0,
      label: "军事人员表",
      labels: ["军事人员表"],
      resourceId: "706ac1cf-f970-4064-8a9c-09e77784be5c",
      entityId: "MP-00001",
      nodeName: "张伟",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        {
          code: "_sys_updateTime",
          name: "_sys_updateTime",
          value: "2025-05-26 23:15:14",
          sortId: 0,
          isSystem: true,
        },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "联系方式", name: "联系方式", value: "13800138000", sortId: 0, isSystem: false },
        { code: "人员编号", name: "人员编号", value: "MP-00001", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "706ac1cf-f970-4064-8a9c-09e77784be5c",
          sortId: 0,
          isSystem: false,
        },
        { code: "性别", name: "性别", value: "男", sortId: 0, isSystem: false },
        { code: "紧急联系人", name: "紧急联系人", value: "张建国（父）13100000001", sortId: 0, isSystem: false },
        { code: "姓名", name: "姓名", value: "张伟", sortId: 0, isSystem: false },
        { code: "军衔", name: "军衔", value: "上尉", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@683ffca5", sortId: 0, isSystem: false },
        { code: "出生日期", name: "出生日期", value: "1995-08-12", sortId: 0, isSystem: false },
        { code: "入伍日期", name: "入伍日期", value: "2013-09-01", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "张伟", sortId: 0, isSystem: true },
        { code: "职务", name: "职务", value: "装甲连连长", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "MP-00001", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:21",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "30",
      nodeId: 30,
      label: "军事训练表",
      labels: ["军事训练表"],
      resourceId: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
      entityId: "TR-23001",
      nodeName: "冬季野外生存训练",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "参与人员数量", name: "参与人员数量", value: "120", sortId: 0, isSystem: false },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "开始日期", name: "开始日期", value: "2023-01-05", sortId: 0, isSystem: false },
        { code: "备注", name: "备注", value: "零下30度极限测试", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
          sortId: 0,
          isSystem: false,
        },
        { code: "训练类型", name: "训练类型", value: "野外生存", sortId: 0, isSystem: false },
        { code: "结束日期", name: "结束日期", value: "2023-01-15", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@7ad36add", sortId: 0, isSystem: false },
        { code: "训练结果", name: "训练结果", value: "优秀", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "冬季野外生存训练", sortId: 0, isSystem: true },
        { code: "训练地点", name: "训练地点", value: "昆仑山训练基地", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "TR-23001", sortId: 0, isSystem: true },
        { code: "训练名称", name: "训练名称", value: "冬季野外生存训练", sortId: 0, isSystem: false },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:32",
          sortId: 0,
          isSystem: true,
        },
        { code: "训练编号", name: "训练编号", value: "TR-23001", sortId: 0, isSystem: false },
      ],
    },
    {
      id: "20",
      nodeId: 20,
      label: "军事人员表",
      labels: ["军事人员表"],
      resourceId: "706ac1cf-f970-4064-8a9c-09e77784be5c",
      entityId: "MP-00021",
      nodeName: "方振华",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        {
          code: "_sys_updateTime",
          name: "_sys_updateTime",
          value: "2025-05-26 23:15:14",
          sortId: 0,
          isSystem: true,
        },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "联系方式", name: "联系方式", value: "13802138000", sortId: 0, isSystem: false },
        { code: "人员编号", name: "人员编号", value: "MP-00021", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "706ac1cf-f970-4064-8a9c-09e77784be5c",
          sortId: 0,
          isSystem: false,
        },
        { code: "性别", name: "性别", value: "男", sortId: 0, isSystem: false },
        { code: "紧急联系人", name: "紧急联系人", value: "方玉梅（母）13300000021", sortId: 0, isSystem: false },
        { code: "姓名", name: "姓名", value: "方振华", sortId: 0, isSystem: false },
        { code: "军衔", name: "军衔", value: "上尉", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@75a1818d", sortId: 0, isSystem: false },
        { code: "出生日期", name: "出生日期", value: "1993-02-14", sortId: 0, isSystem: false },
        { code: "入伍日期", name: "入伍日期", value: "2011-04-15", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "方振华", sortId: 0, isSystem: true },
        { code: "职务", name: "职务", value: "侦察排长", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "MP-00021", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:21",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "10",
      nodeId: 10,
      label: "军事人员表",
      labels: ["军事人员表"],
      resourceId: "706ac1cf-f970-4064-8a9c-09e77784be5c",
      entityId: "MP-00011",
      nodeName: "孙志勇",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        {
          code: "_sys_updateTime",
          name: "_sys_updateTime",
          value: "2025-05-26 23:15:14",
          sortId: 0,
          isSystem: true,
        },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "联系方式", name: "联系方式", value: "13801138000", sortId: 0, isSystem: false },
        { code: "人员编号", name: "人员编号", value: "MP-00011", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "706ac1cf-f970-4064-8a9c-09e77784be5c",
          sortId: 0,
          isSystem: false,
        },
        { code: "性别", name: "性别", value: "男", sortId: 0, isSystem: false },
        { code: "紧急联系人", name: "紧急联系人", value: "孙丽华（妻）13200000011", sortId: 0, isSystem: false },
        { code: "姓名", name: "姓名", value: "孙志勇", sortId: 0, isSystem: false },
        { code: "军衔", name: "军衔", value: "大校", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@27af14ff", sortId: 0, isSystem: false },
        { code: "出生日期", name: "出生日期", value: "1991-10-15", sortId: 0, isSystem: false },
        { code: "入伍日期", name: "入伍日期", value: "2009-08-01", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "孙志勇", sortId: 0, isSystem: true },
        { code: "职务", name: "职务", value: "旅参谋长", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "MP-00011", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:21",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "47",
      nodeId: 47,
      label: "军事训练表",
      labels: ["军事训练表"],
      resourceId: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
      entityId: "TR-23018",
      nodeName: "装甲维修技能大赛",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "参与人员数量", name: "参与人员数量", value: "50", sortId: 0, isSystem: false },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "开始日期", name: "开始日期", value: "2023-09-18", sortId: 0, isSystem: false },
        { code: "备注", name: "备注", value: "故障修复用时破纪录", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
          sortId: 0,
          isSystem: false,
        },
        { code: "训练类型", name: "训练类型", value: "技术比武", sortId: 0, isSystem: false },
        { code: "结束日期", name: "结束日期", value: "2023-09-20", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@4ea0bbb", sortId: 0, isSystem: false },
        { code: "训练结果", name: "训练结果", value: "卓越", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "装甲维修技能大赛", sortId: 0, isSystem: true },
        { code: "训练地点", name: "训练地点", value: "装甲兵工程学院", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "TR-23018", sortId: 0, isSystem: true },
        { code: "训练名称", name: "训练名称", value: "装甲维修技能大赛", sortId: 0, isSystem: false },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:32",
          sortId: 0,
          isSystem: true,
        },
        { code: "训练编号", name: "训练编号", value: "TR-23018", sortId: 0, isSystem: false },
      ],
    },
    {
      id: "56",
      nodeId: 56,
      label: "军事训练表",
      labels: ["军事训练表"],
      resourceId: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
      entityId: "TR-23027",
      nodeName: "伪装侦察特训",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "参与人员数量", name: "参与人员数量", value: "35", sortId: 0, isSystem: false },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "开始日期", name: "开始日期", value: "2023-09-25", sortId: 0, isSystem: false },
        { code: "备注", name: "备注", value: "热成像对抗", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
          sortId: 0,
          isSystem: false,
        },
        { code: "训练类型", name: "训练类型", value: "侦察训练", sortId: 0, isSystem: false },
        { code: "结束日期", name: "结束日期", value: "2023-09-27", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@bc19880", sortId: 0, isSystem: false },
        { code: "训练结果", name: "训练结果", value: "良好", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "伪装侦察特训", sortId: 0, isSystem: true },
        { code: "训练地点", name: "训练地点", value: "多地形训练场", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "TR-23027", sortId: 0, isSystem: true },
        { code: "训练名称", name: "训练名称", value: "伪装侦察特训", sortId: 0, isSystem: false },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:32",
          sortId: 0,
          isSystem: true,
        },
        { code: "训练编号", name: "训练编号", value: "TR-23027", sortId: 0, isSystem: false },
      ],
    },
    {
      id: "76",
      nodeId: 76,
      label: "军事作战单位表",
      labels: ["军事作战单位表"],
      resourceId: "fd0fd40e-d289-4b1f-9e1e-83c0258091d8",
      entityId: "UN-001",
      nodeName: "第1装甲师",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "驻地", name: "驻地", value: "华北平原", sortId: 0, isSystem: false },
        { code: "装备数量", name: "装备数量", value: "420", sortId: 0, isSystem: false },
        { code: "指挥官编号", name: "指挥官编号", value: "MP-00013", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "fd0fd40e-d289-4b1f-9e1e-83c0258091d8",
          sortId: 0,
          isSystem: false,
        },
        { code: "单位名称", name: "单位名称", value: "第1装甲师", sortId: 0, isSystem: false },
        { code: "单位类型", name: "单位类型", value: "陆军", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@2070dfec", sortId: 0, isSystem: false },
        { code: "编制人数", name: "编制人数", value: "8500", sortId: 0, isSystem: false },
        { code: "作战状态", name: "作战状态", value: "全面就绪", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "第1装甲师", sortId: 0, isSystem: true },
        { code: "成立日期", name: "成立日期", value: "1970-01-01", sortId: 0, isSystem: false },
        { code: "上级单位编号", name: "上级单位编号", value: "", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "UN-001", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 11:51:44",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
  ],
  links: [
    {
      id: "39",
      linkId: 39,
      name: "参与训练",
      source: "0",
      target: "30",
      sourceName: "张伟",
      targetName: "冬季野外生存训练",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "45",
      linkId: 45,
      name: "参与训练",
      source: "20",
      target: "30",
      sourceName: "方振华",
      targetName: "冬季野外生存训练",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "42",
      linkId: 42,
      name: "参与训练",
      source: "10",
      target: "30",
      sourceName: "孙志勇",
      targetName: "冬季野外生存训练",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "40",
      linkId: 40,
      name: "参与训练",
      source: "0",
      target: "47",
      sourceName: "张伟",
      targetName: "装甲维修技能大赛",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "46",
      linkId: 46,
      name: "参与训练",
      source: "20",
      target: "47",
      sourceName: "方振华",
      targetName: "装甲维修技能大赛",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "43",
      linkId: 43,
      name: "参与训练",
      source: "10",
      target: "47",
      sourceName: "孙志勇",
      targetName: "装甲维修技能大赛",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "41",
      linkId: 41,
      name: "参与训练",
      source: "0",
      target: "56",
      sourceName: "张伟",
      targetName: "伪装侦察特训",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "44",
      linkId: 44,
      name: "参与训练",
      source: "10",
      target: "56",
      sourceName: "孙志勇",
      targetName: "伪装侦察特训",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "47",
      linkId: 47,
      name: "参与训练",
      source: "20",
      target: "56",
      sourceName: "方振华",
      targetName: "伪装侦察特训",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "192",
      linkId: 192,
      name: "隶属关系",
      source: "76",
      target: "0",
      sourceName: "第1装甲师",
      targetName: "张伟",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 15:09:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "5a715319-d9f6-40d2-8a62-59963a80733e",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "194",
      linkId: 194,
      name: "隶属关系",
      source: "76",
      target: "20",
      sourceName: "第1装甲师",
      targetName: "方振华",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 15:09:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "5a715319-d9f6-40d2-8a62-59963a80733e",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "193",
      linkId: 193,
      name: "隶属关系",
      source: "76",
      target: "10",
      sourceName: "第1装甲师",
      targetName: "孙志勇",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 15:09:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "5a715319-d9f6-40d2-8a62-59963a80733e",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
  ],
  renderType: null,
  columns: null,
  rows: null,
};

// 添加行动信息相关状态
const activeCollapse = ref(["action"]); // 默认展开
const actionInfo = ref([
  { label: "行动名称", value: "", key: "name" },
  { label: "开始时间", value: "", key: "startTime" },
  { label: "任务时长", value: "", key: "duration" },
  { label: "作战方向", value: "", key: "zzfsType" },
  { label: "区域范围", value: "", key: "position" },
  { label: "行动描述", value: "", key: "description" },
]);

/* ================== 面板状态 ================== */
const panel = reactive({
  visible: false,
  title: "",
  sub: "",
  attrs: [],
});

/* ================== 颜色：固定 + 自动补齐（最多6种） ================== */
const COLOR_BY_TYPE = {
  unit: { fill: "#FFC857", stroke: "#B8860B" }, // 示例已知
  person: { fill: "#4F8DF7", stroke: "#1F5CC4" },
  train: { fill: "#33C08D", stroke: "#1E8A62" },
  other: { fill: "#a9a9a9", stroke: "#6e6e6e" },
};
const PALETTE = [
  { fill: "#FFC857", stroke: "#B8860B" },
  { fill: "#4F8DF7", stroke: "#1F5CC4" },
  { fill: "#33C08D", stroke: "#1E8A62" },
  { fill: "#A47CF3", stroke: "#6A4FB6" },
  { fill: "#FF7A90", stroke: "#C94A5A" },
  { fill: "#8BD3E6", stroke: "#4E98A8" },
];

/* ================== 通用树构造（只看 links 方向） ================== */
/**
 * 通用版：仅根据 links 构造森林（children-only 结构）。
 * - link.source → link.target 当作 父→子；如果有关系需要反向，可在 options.reverseByRelation 里写名字
 * - 多父会为 child 按“父亲上下文”克隆（id 带 @p:<parentId>）
 * - 检测环并截断（避免死循环）
 */
function buildForestFromLinks(data, options = {}) {
  const { reverseByRelation = new Set(), maxDepth = 16 } = options;

  const nodes = data.nodes || [];
  const links = data.links || [];
  const nodeById = new Map(nodes.map((n) => [String(n.id), n]));

  const getTypeKey = (n) => n?.labels?.[0] || n?.label || "other";
  const getAttr = (n, code) => n?.attrs?.find((a) => a.code === code || a.name === code)?.value ?? "";
  const getName = (n) => n?.nodeName || getAttr(n, "name") || n?.entityId || n?.id || "";

  const makeId = (type, id, ctx) => (ctx ? `${type}:${id}@${ctx}` : `${type}:${id}`);
  const toTreeNode = (n, type, ctx, parentRelation) => ({
    id: makeId(type, String(n.id), ctx),
    name: getName(n),
    data: {
      originId: String(n.id),
      entityId: n.entityId,
      labels: n.labels,
      type,
      attrs: n.attrs,
      parentRelation: parentRelation || "",
    },
  });

  // 出边 + 入度
  const out = new Map(); // id -> [{ to, relation }]
  const indeg = new Map(); // id -> number
  for (const e of links) {
    const rel = e.name || "";
    let s = String(e.source);
    let t = String(e.target);
    if (reverseByRelation.has(rel)) {
      const tmp = s;
      s = t;
      t = tmp;
    }
    if (!out.has(s)) out.set(s, []);
    out.get(s).push({ to: t, relation: rel });
    indeg.set(t, (indeg.get(t) || 0) + 1);
  }

  const allIds = nodes.map((n) => String(n.id));
  const rootIds = allIds.filter((id) => !indeg.get(id));

  function dfs(curId, ctx, depth, stack) {
    const n = nodeById.get(curId);
    if (!n) return null;
    const type = getTypeKey(n);
    const node = toTreeNode(n, type, ctx, stack?.parentRelation);
    if (depth >= maxDepth) return node;

    const seen = stack?.set || new Set();
    if (seen.has(curId)) return node; // 环，截断

    const nextSeen = new Set(seen);
    nextSeen.add(curId);

    const children = [];
    const outs = out.get(curId) || [];
    for (const e of outs) {
      const childCtx = `p:${curId}`;
      const child = dfs(e.to, childCtx, depth + 1, {
        set: nextSeen,
        parentRelation: e.relation,
      });
      if (child) children.push(child);
    }
    if (children.length) node.children = children;
    return node;
  }

  const forest = [];
  if (rootIds.length) {
    for (const rid of rootIds) {
      const sub = dfs(rid, "", 0, { set: new Set(), parentRelation: "" });
      if (sub) forest.push(sub);
    }
    return forest;
  }

  // 没有根（全环）→ 虚拟根
  const virtualChildren = [];
  for (const id of allIds) {
    const sub = dfs(id, "root", 0, { set: new Set(), parentRelation: "" });
    if (sub) virtualChildren.push(sub);
  }
  return [{ id: "ROOT", name: "ROOT", data: { type: "other" }, children: virtualChildren }];
}

/* ================== 边样式 ================== */
const EDGE_STYLE = {
  endArrow: true,
  labelText: (d) => d?.data?.relation ?? "",
  labelPlacement: "center",
  labelAutoRotate: false, // 性能更稳
  labelBackground: true,
  labelPadding: [2, 6, 2, 6],
};

/* ================== 叶子判断（仅用于标签朝向） ================== */
function isLeafNode(d) {
  return !d?.children || d.children.length === 0;
}

/* ================== 图实例 & 布局切换 ================== */
const graphRef = ref(null);

const switchLayout = (type) => {
  const g = graphRef.value;
  if (!g) return;
  if (type === "tree") {
    g.setLayout({ type: "dendrogram", direction: "TB", nodeSep: 150, rankSep: 200 });
    g.setEdge({ type: "cubic-vertical", style: EDGE_STYLE, animation: { enter: false } });
  } else if (type === "dendrogram") {
    g.setLayout({ type: "dendrogram", direction: "LR", nodeSep: 130, rankSep: 200 });
    g.setEdge({ type: "cubic-horizontal", style: EDGE_STYLE, animation: { enter: false } });
  } else if (type === "radial") {
    g.setLayout({ type: "dendrogram", radial: true, nodeSep: 130, rankSep: 200 });
    g.setEdge({ type: "line", style: EDGE_STYLE, animation: { enter: false } });
  }
  g.layout();
  g.fitView(24);
};

onMounted(() => {
  /* 1) 用“泛化关系”构造森林 & 根 */
  const forests = buildForestFromLinks(data /*, { reverseByRelation: new Set(['如果有要反向的关系名放这里']) } */);
  const treeRoot = forests.length === 1 ? forests[0] : { id: "ROOT", name: "ROOT", children: forests };

  /* 2) 生成 graphData，并把真实关系名写到每条边的 data.relation */
  const gData = treeToGraphData(treeRoot);
  const nodeIndex = new Map((gData.nodes || []).map((n) => [String(n.id), n]));
  gData.edges = (gData.edges || []).map((e) => {
    const relation = nodeIndex.get(String(e.target))?.data?.parentRelation || "";
    return { ...e, data: { ...(e.data || {}), relation } };
  });

  /* 3) 自动扩展颜色（最多 6 种），优先用你手动定义的 */
  const ALL_TYPES = Array.from(new Set((gData.nodes || []).map((n) => n.data?.type || "other")));
  const COLOR_AUTO = {};
  let i = 0;
  for (const t of ALL_TYPES) {
    COLOR_AUTO[t] = COLOR_BY_TYPE[t] || PALETTE[i++ % PALETTE.length];
  }

  /* 4) 创建图 */
  const g = (graphRef.value = new Graph({
    container: "graph-container",
    autoFit: "view",
    data: gData,
    node: {
      type: "rect",
      style: (d) => {
        const t = d?.data?.type ?? "other";
        const { fill, stroke } = COLOR_AUTO[t] || COLOR_BY_TYPE.other;
        return {
          fill,
          stroke,
          lineWidth: 1.5,
          radius: 8,
          padding: [4, 8, 4, 8],
          labelText: d.name ?? d.id,
          labelFill: "#fff",
          labelPlacement: isLeafNode(d) ? "right" : "left",
          labelBackground: true,
          draggable: false, // 强制不可拖拽节点
        };
      },
      animation: { enter: false },
    },

    edge: {
      type: "cubic-horizontal",
      style: EDGE_STYLE,
      animation: { enter: false },
    },
    layout: { type: "dendrogram", direction: "LR", nodeSep: 130, rankSep: 200 },
    // 只开画布拖拽与缩放
    behaviors: [
      { type: "drag-canvas", enable: true },
      { type: "zoom-canvas", enable: true },
    ],
    // 悬浮 Tooltip（预览属性前几条）
    plugins: [
      {
        type: "tooltip",
        key: "node-tooltip",
        itemTypes: ["node"],
        trigger: "click",
        getContent: (e) => {
          // 通过图实例获取完整的节点数据
          if (e.targetType !== "node") return "";
          const nodeId = e?.target?.id;
          if (!nodeId) return "";
          const nodeData = g.getNodeData(nodeId);
          const attrs = nodeData?.data?.attrs || [];
          const top = attrs
            // .slice(0, 6)
            .map((a) => `<div class="row"><b>${a.name ?? a.code}</b>：${a.value ?? ""}</div>`)
            .join("");
          return `<div class="g6-tip"><div class="hd">${nodeData?.name ?? nodeId}</div>${top || "<i>无属性</i>"}</div>`;
        },
        shouldBegin: (e) => e?.target?.type === "node",
        offsetX: 12,
        offsetY: 8,
      },
    ],
    theme: "dark",
  }));

  g.render();
});

onBeforeUnmount(() => {
  graphRef.value?.destroy();
  graphRef.value = null;
});
</script>

<style scoped>
.wrap {
  position: relative;
}
.toolbar {
  display: flex;
  gap: 8px;
  position: absolute;
  top: 0px;
  left: 0px;
}
.graph {
  width: 100%;
  height: 800px;
  cursor: grab;
}
.graph:active {
  cursor: grabbing;
}

.info-panel {
  position: absolute;
  right: 12px;
  top: 64px;
  width: 340px;
  max-height: calc(100% - 96px);
  background: #111;
  color: #fff;
  border: 1px solid #333;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.35);
}
.panel-hd {
  display: flex;
  align-items: start;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid #222;
}
.title {
  font-weight: 700;
  font-size: 16px;
  flex: 1;
}
.sub {
  color: #aaa;
  font-size: 12px;
  margin-top: 2px;
}
.close {
  background: transparent;
  border: none;
  color: #aaa;
  font-size: 20px;
  cursor: pointer;
}
.panel-bd {
  padding: 10px 12px;
  max-height: 520px;
  overflow: auto;
}
.kv {
  display: grid;
  grid-template-columns: 110px 1fr;
  gap: 6px 10px;
  padding: 6px 0;
  border-bottom: 1px dashed #222;
}
.k {
  color: #9ecbff;
  word-break: break-all;
}
.v {
  color: #e8e8e8;
  word-break: break-all;
}

.g6-tip {
  max-width: 320px;
  background: rgba(0, 0, 0, 0.9);
  color: #fff;
  border: 1px solid #333;
  padding: 8px 10px;
  border-radius: 6px;
}
.g6-tip .hd {
  font-weight: 700;
  margin-bottom: 6px;
}
.g6-tip .row {
  font-size: 12px;
  margin: 2px 0;
}

.action-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 280px;
  border-radius: 8px;
  z-index: 100;
}

.action-info {
  padding: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 13px;
}

.label {
  color: #409eff;
  min-width: 70px;
  flex-shrink: 0;
}

.value {
  color: #111;
  flex: 1;
  word-break: break-all;
}

.action-panel :deep(.el-collapse-item__header) {
  padding: 12px 15px;
}

.action-panel :deep(.el-collapse-item__content) {
  padding: 0;
}
</style>
