<template>
  <!-- 可叠加在任意容器(如 RTSP 播放器)左下角的遥感控件 -->
  <div class="uav-widget" :class="[`anchor-${anchor}`]" :style="inlineOffset" @contextmenu.prevent>
    <!-- 摇杆盘 -->
    <div
      ref="padRef"
      class="stick-pad"
      :style="{ width: size + 'px', height: size + 'px' }"
      @pointerdown="onPadDown"
      @wheel.prevent="onWheelZoom"
      title="拖拽控制云台，滚轮缩放"
    >
      <div class="ring"></div>
      <div class="cross cross-x"></div>
      <div class="cross cross-y"></div>
      <div class="thumb" :style="thumbStyle"></div>
    </div>

    <!-- 右侧竖排按钮 -->
    <div class="side-buttons">
      <el-button @click="zoomIn" title="放大" :icon="Plus" circle></el-button>
      <el-button @click="zoomOut" title="缩小" :icon="Minus" circle></el-button>
    </div>

    <!-- 底部功能条（示例 10 个，可按需修改/隐藏） -->
    <div class="bottom-bar" v-if="showBottomBar">
      <el-tooltip v-for="b in bottomBtns" :key="b.key" :content="b.title" placement="top" :disabled="!showBottomBar">
        <el-button circle :key="b.key" @click="emitAction(b.key)" :icon="b.icon"> </el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, reactive, ref, computed, nextTick, watch, defineExpose } from "vue";
import { CameraFilled, Plus, Minus } from "@element-plus/icons-vue";

const emit = defineEmits(["gimbal", "zoom", "action"]);

// ====== Props ======
const props = defineProps({
  size: { type: Number, default: 160 },
  deadzone: { type: Number, default: 0.12 },
  sendHz: { type: Number, default: 20 },
  maxYaw: { type: Number, default: 120 },
  maxPitch: { type: Number, default: 90 },
  inertiaMs: { type: Number, default: 120 },
  zoom: { type: Number, default: 1 },
  zoomMin: { type: Number, default: 1 },
  zoomMax: { type: Number, default: 20 },
  zoomStep: { type: Number, default: 0.1 },
  anchor: { type: String, default: "bottom-left" }, // bottom-left|bottom-right|top-left|top-right
  offset: { type: Object, default: () => ({ left: 16, bottom: 16, right: "auto", top: "auto" }) },
  showBottomBar: { type: Boolean, default: true },
});

// ====== State ======
const padRef = ref(null);
const center = reactive({ x: 0, y: 0, r: 0 });
const thumb = reactive({ x: 0, y: 0 }); // 像素坐标（相对中心）
const norm = reactive({ x: 0, y: 0 }); // 归一化 -1~1（死区后）
const cmd = reactive({ yaw: 0, pitch: 0 });
const zoomVal = ref(props.zoom);
let dragging = false;
let sendTimer = null;
let lastSent = 0;

// 底部按钮（示例）
const bottomBtns = [
  { key: "video", title: "视频", icon: CameraFilled },
  { key: "photo", title: "拍照", icon: CameraFilled },
  { key: "display", title: "显示", icon: CameraFilled },
  { key: "stream", title: "串流", icon: CameraFilled },
  { key: "settings", title: "设置", icon: CameraFilled },
  { key: "stabilize", title: "防抖", icon: CameraFilled },
  { key: "grid", title: "网格", icon: CameraFilled },
  { key: "osd", title: "OSD", icon: CameraFilled },
  { key: "record", title: "录像", icon: CameraFilled },
  { key: "download", title: "下载", icon: CameraFilled },
];

// ====== Computed ======
const thumbStyle = computed(() => ({ transform: `translate(${thumb.x - 17}px, ${thumb.y - 17}px)` }));
const inlineOffset = computed(() => ({
  left: addPx(props.offset.left),
  right: addPx(props.offset.right),
  top: addPx(props.offset.top),
  bottom: addPx(props.offset.bottom),
}));

function addPx(v) {
  return typeof v === "number" ? `${v}px` : v ?? "auto";
}

// ====== Helpers ======
const clamp = (n, min, max) => Math.min(max, Math.max(min, n));
const len = (x, y) => Math.hypot(x, y);

function computeCenter() {
  const rect = padRef.value.getBoundingClientRect();
  center.x = rect.left + rect.width / 2;
  center.y = rect.top + rect.height / 2;
  center.r = rect.width / 2 - 16;
}

function applyDead(nx, ny) {
  const r = Math.hypot(nx, ny);
  if (r < props.deadzone) return { x: 0, y: 0 };
  const k = (r - props.deadzone) / (1 - props.deadzone);
  const scale = k / (r || 1);
  return { x: nx * scale, y: ny * scale };
}

function pixelToNorm(dx, dy) {
  const nx = clamp(dx / center.r, -1, 1);
  const ny = clamp(dy / center.r, -1, 1);
  return applyDead(nx, ny);
}

function normToCmd(n) {
  return { yaw: n.x * props.maxYaw, pitch: -n.y * props.maxPitch };
}

// ====== Pointer Handlers ======
function onPadDown(e) {
  e.currentTarget.setPointerCapture(e.pointerId);
  dragging = true;
  updateFromPointer(e);
}
function onPadMove(e) {
  if (!dragging) return;
  updateFromPointer(e);
}
function onPadUp(e) {
  if (!dragging) return;
  dragging = false;
  e.currentTarget.releasePointerCapture(e.pointerId);
  backToCenter();
}
function onWheelZoom(e) {
  const delta = e.deltaY > 0 ? -props.zoomStep : props.zoomStep;
  setZoom(zoomVal.value + delta);
}

function updateFromPointer(e) {
  const dx = e.clientX - center.x;
  const dy = e.clientY - center.y;
  const L = len(dx, dy);
  const rate = L > center.r ? center.r / L : 1;
  thumb.x = dx * rate;
  thumb.y = dy * rate;
  const n = pixelToNorm(thumb.x, thumb.y);
  norm.x = n.x;
  norm.y = n.y;
  const c = normToCmd(n);
  cmd.yaw = c.yaw;
  cmd.pitch = c.pitch;
}

function backToCenter() {
  const sx = thumb.x,
    sy = thumb.y,
    snx = norm.x,
    sny = norm.y;
  const t0 = performance.now();
  const step = (now) => {
    const t = clamp((now - t0) / props.inertiaMs, 0, 1);
    const ease = 1 - Math.pow(1 - t, 3);
    thumb.x = sx * (1 - ease);
    thumb.y = sy * (1 - ease);
    norm.x = snx * (1 - ease);
    norm.y = sny * (1 - ease);
    const c = normToCmd(norm);
    cmd.yaw = c.yaw;
    cmd.pitch = c.pitch;
    if (t < 1) requestAnimationFrame(step);
  };
  requestAnimationFrame(step);
}

// ====== Emit / API ======
function startLoop() {
  clearInterval(sendTimer);
  sendTimer = setInterval(() => {
    const now = Date.now();
    const moving = Math.abs(cmd.yaw) > 0.01 || Math.abs(cmd.pitch) > 0.01;
    if (moving || now - lastSent > 350) {
      emit("gimbal", { yaw: cmd.yaw, pitch: cmd.pitch });
      lastSent = now;
    }
  }, 1000 / props.sendHz);
}

function setZoom(z) {
  zoomVal.value = clamp(+z.toFixed(2), props.zoomMin, props.zoomMax);
  emit("zoom", { zoom: zoomVal.value });
}
function zoomIn() {
  setZoom(zoomVal.value + props.zoomStep);
}
function zoomOut() {
  setZoom(zoomVal.value - props.zoomStep);
}
function emitAction(key) {
  emit("action", key);
}
function centerStick() {
  thumb.x = thumb.y = 0;
  norm.x = norm.y = 0;
  cmd.yaw = cmd.pitch = 0;
}

defineExpose({ centerStick, zoomIn, zoomOut });

// ====== Lifecycle ======
onMounted(() => {
  nextTick(() => {
    computeCenter();
    window.addEventListener("resize", computeCenter);
    const pad = padRef.value;
    pad.addEventListener("pointermove", onPadMove);
    pad.addEventListener("pointerup", onPadUp);
    pad.addEventListener("pointercancel", onPadUp);
    pad.addEventListener("dblclick", centerStick);
    startLoop();
  });
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", computeCenter);
  const pad = padRef.value;
  if (pad) {
    pad.removeEventListener("pointermove", onPadMove);
    pad.removeEventListener("pointerup", onPadUp);
    pad.removeEventListener("pointercancel", onPadUp);
  }
  clearInterval(sendTimer);
});
</script>

<style scoped>
.uav-widget {
  /* position: absolute; */
  pointer-events: auto;
  user-select: none;
}
.uav-widget.anchor-bottom-left {
  left: 16px;
  bottom: 16px;
}
.uav-widget.anchor-bottom-right {
  right: 16px;
  bottom: 16px;
}
.uav-widget.anchor-top-left {
  left: 16px;
  top: 16px;
}
.uav-widget.anchor-top-right {
  right: 16px;
  top: 16px;
}

.stick-pad {
  position: relative;
  border-radius: 50%;
  background: #0b0b0b;
  touch-action: none;
  user-select: none;
  box-shadow: inset 0 0 0 2px rgba(100, 80, 70, 0.6), 0 10px 30px rgba(0, 0, 0, 0.45);
}
.ring {
  position: absolute;
  inset: 12px;
  border-radius: 50%;
  border: 2px solid rgba(70, 200, 200, 0.35);
}
.cross {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.cross-x {
  width: 76%;
  border-top: 1px dashed rgba(220, 220, 220, 0.2);
}
.cross-y {
  height: 76%;
  border-left: 1px dashed rgba(220, 220, 220, 0.2);
}
.thumb {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 34px;
  height: 34px;
  transform: translate(-17px, -17px);
  border-radius: 50%;
  background: radial-gradient(16px circle at 35% 35%, #8a5140, #4a2c22 70%);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.45), inset 0 0 0 2px rgba(255, 255, 255, 0.06);
  pointer-events: none;
}

.side-buttons {
  position: absolute;
  right: 0px;
  top: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.bottom-bar {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -54px;
  display: flex;
  gap: 12px;
}

:deep(.el-button + .el-button) {
  margin-left: 0 !important;
}
</style>
