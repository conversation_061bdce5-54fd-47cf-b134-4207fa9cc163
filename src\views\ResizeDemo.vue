<template>
  <div class="resize-demo">
    <h1>拖拽调整大小指令演示</h1>

    <div class="demo-section">
      <h2>基础用法</h2>
      <p>拖拽右下角、右边缘或下边缘来调整大小</p>
      <div
        class="resizable-box basic"
        v-resize="{
          minWidth: 150,
          minHeight: 100,
          maxWidth: 600,
          maxHeight: 400,
          onResize: onBasicResize,
        }"
      >
        <div class="content">
          <h3>可调整大小的容器</h3>
          <p>当前尺寸: {{ basicSize.width }}px × {{ basicSize.height }}px</p>
          <p>拖拽边缘来调整大小</p>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>所有方向调整</h2>
      <p>支持8个方向的拖拽调整</p>
      <div
        class="resizable-box all-directions"
        v-resize="{
          handles: ['n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw'],
          minWidth: 200,
          minHeight: 150,
          handleSize: 10,
          onResize: onAllDirectionsResize,
        }"
      >
        <div class="content">
          <h3>全方向调整</h3>
          <p>当前尺寸: {{ allDirectionsSize.width }}px × {{ allDirectionsSize.height }}px</p>
          <p>拖拽方向: {{ allDirectionsSize.direction || "无" }}</p>
          <p>可以从任意边缘或角落拖拽</p>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>保持宽高比</h2>
      <p>拖拽时保持原始宽高比</p>
      <div
        class="resizable-box aspect-ratio"
        v-resize="{
          handles: ['se', 'ne', 'sw', 'nw'],
          preserveAspectRatio: true,
          minWidth: 100,
          minHeight: 100,
          onResize: onAspectRatioResize,
        }"
      >
        <div class="content">
          <h3>保持宽高比</h3>
          <p>当前尺寸: {{ aspectRatioSize.width }}px × {{ aspectRatioSize.height }}px</p>
          <p>宽高比: {{ aspectRatio }}</p>
          <div class="image-placeholder">📷 图片占位符</div>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>隐藏手柄</h2>
      <p>手柄默认隐藏，鼠标悬停时显示</p>
      <div
        class="resizable-box hidden-handles"
        v-resize="{
          handles: ['e', 's', 'se'],
          showHandles: false,
          minWidth: 150,
          minHeight: 100,
          onResize: onHiddenHandlesResize,
        }"
      >
        <div class="content">
          <h3>隐藏手柄模式</h3>
          <p>当前尺寸: {{ hiddenHandlesSize.width }}px × {{ hiddenHandlesSize.height }}px</p>
          <p>将鼠标移到边缘查看手柄</p>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>API 方法演示</h2>
      <div class="controls">
        <button @click="resetSize" class="btn">重置大小</button>
        <button @click="setCustomSize" class="btn">设置为 300×200</button>
        <button @click="getCurrentSize" class="btn">获取当前大小</button>
      </div>
      <div
        ref="apiDemoRef"
        class="resizable-box api-demo"
        v-resize="{
          handles: ['e', 's', 'se'],
          minWidth: 200,
          minHeight: 150,
          onResize: onApiDemoResize,
        }"
      >
        <div class="content">
          <h3>API 演示</h3>
          <p>当前尺寸: {{ apiDemoSize.width }}px × {{ apiDemoSize.height }}px</p>
          <p>使用上方按钮测试API方法</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";

// 基础用法
const basicSize = ref({ width: 300, height: 200 });
const onBasicResize = (data) => {
  basicSize.value = { width: data.width, height: data.height };
};

// 全方向调整
const allDirectionsSize = ref({ width: 350, height: 250, direction: null });
const onAllDirectionsResize = (data) => {
  allDirectionsSize.value = {
    width: data.width,
    height: data.height,
    direction: data.direction,
  };
};

// 保持宽高比
const aspectRatioSize = ref({ width: 300, height: 200 });
const onAspectRatioResize = (data) => {
  aspectRatioSize.value = { width: data.width, height: data.height };
};
const aspectRatio = computed(() => {
  if (aspectRatioSize.value.height === 0) return "0";
  return (aspectRatioSize.value.width / aspectRatioSize.value.height).toFixed(2);
});

// 隐藏手柄
const hiddenHandlesSize = ref({ width: 280, height: 180 });
const onHiddenHandlesResize = (data) => {
  hiddenHandlesSize.value = { width: data.width, height: data.height };
};

// API 演示
const apiDemoRef = ref(null);
const apiDemoSize = ref({ width: 300, height: 200 });
const onApiDemoResize = (data) => {
  apiDemoSize.value = { width: data.width, height: data.height };
};

const resetSize = () => {
  if (apiDemoRef.value && apiDemoRef.value._resizeAPI) {
    apiDemoRef.value._resizeAPI.setSize(300, 200);
    apiDemoSize.value = { width: 300, height: 200 };
  }
};

const setCustomSize = () => {
  if (apiDemoRef.value && apiDemoRef.value._resizeAPI) {
    apiDemoRef.value._resizeAPI.setSize(300, 200);
    apiDemoSize.value = { width: 300, height: 200 };
  }
};

const getCurrentSize = () => {
  if (apiDemoRef.value && apiDemoRef.value._resizeAPI) {
    const size = apiDemoRef.value._resizeAPI.getSize();
    alert(`当前大小: ${size.width}px × ${size.height}px`);
  }
};
</script>

<style scoped>
.resize-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.demo-section h2 {
  margin-top: 0;
  color: #333;
}

.demo-section p {
  color: #666;
  margin-bottom: 15px;
}

.resizable-box {
  border: 2px solid #007bff;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  display: inline-block;
  min-width: 200px;
  min-height: 150px;
}

.resizable-box.basic {
  width: 300px;
  height: 200px;
}

.resizable-box.all-directions {
  width: 350px;
  height: 250px;
}

.resizable-box.aspect-ratio {
  width: 300px;
  height: 200px;
}

.resizable-box.hidden-handles {
  width: 280px;
  height: 180px;
}

.resizable-box.api-demo {
  width: 300px;
  height: 200px;
}

.content {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
}

.content h3 {
  margin-top: 0;
  color: #007bff;
}

.content p {
  margin: 10px 0;
  color: #555;
}

.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border: 2px dashed #ccc;
  height: 80px;
  margin-top: 10px;
  font-size: 24px;
}

.controls {
  margin-bottom: 20px;
}

.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  margin-right: 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn:hover {
  background: #0056b3;
}

/* 自定义手柄样式 */
:deep(.resize-handle) {
  transition: background-color 0.2s ease;
}

:deep(.resize-handle-se) {
  border-bottom-right-radius: 6px;
}

:deep(.resize-handle-ne) {
  border-top-right-radius: 6px;
}

:deep(.resize-handle-sw) {
  border-bottom-left-radius: 6px;
}

:deep(.resize-handle-nw) {
  border-top-left-radius: 6px;
}
</style>
