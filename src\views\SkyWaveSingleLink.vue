<template>
  <div>
    <div class="mb-4">
      <el-button type="primary" @click="play" class="px-4 py-2 my-5 bg-blue-500 text-white rounded hover:bg-blue-600">
        播放
      </el-button>
      <el-button type="primary" @click="stop" class="px-4 py-2 my-5 bg-blue-500 text-white rounded hover:bg-blue-600">
        结束
      </el-button>
    </div>
    <video ref="videoRef" controls muted width="800" height="450"></video>
  </div>
</template>

<script setup>
import { ref, reactive, onUnmounted } from "vue";
import flvjs from "flv.js";
import axios from "axios";

// 定义props
const props = defineProps({
  msg: String,
});

// 响应式数据
const form = reactive({
  rtspUrl: "rtsp://rtspstream:<EMAIL>/movie",
});

// 视频元素引用
const videoRef = ref(null);
// FLV播放器实例
let flvPlayer = null;

// 播放方法
const play = async () => {
  try {
    const res = await axios({
      url: "http://***********:9085/video/getVideoUrl",
      method: "post",
      data: form,
    });

    console.log("获取视频地址成功:", res.data);
    initVideo(res.data.data);
  } catch (error) {
    console.error("播放失败:", error);
    // 这里可以添加错误提示
  }
};

const stop = () => {
  if (flvPlayer) {
    flvPlayer.pause();
    flvPlayer.destroy();
    flvPlayer = null;
  }
};

// 初始化视频播放器
const initVideo = (src) => {
  if (flvjs.isSupported() && videoRef.value) {
    // 如果已有播放器实例，先销毁
    if (flvPlayer) {
      flvPlayer.destroy();
    }

    flvPlayer = flvjs.createPlayer({
      type: "flv",
      url: src,
      enableStashBuffer: true,
      withCredentials: false,
      hasAudio: false,
      hasVideo: true,
      isLive: true,
      stashInitialSize: 100,
    });

    flvPlayer.attachMediaElement(videoRef.value);
    flvPlayer.load();
    flvPlayer.play();

    console.log("FLV播放器初始化成功:", flvPlayer);

    // 添加错误处理
    flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail) => {
      console.error("视频播放错误:", errorType, errorDetail);
    });
  }
};

// 组件卸载时销毁播放器
onUnmounted(() => {
  if (flvPlayer) {
    flvPlayer.destroy();
    flvPlayer = null;
  }
});
</script>

<style scoped></style>
