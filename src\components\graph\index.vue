<template>
  <div ref="wrapEl" class="wrap" :class="{ 'drag-mode': isDragging }">
    <div ref="containerEl" class="graph"></div>

    <!-- 工具条：右上角横向图标 -->
    <div class="tooldock">
      <div class="dock">
        <!-- 回到中心 -->
        <el-tooltip content="回到中心点" placement="bottom" :show-after="300">
          <button class="dock-btn" @click="handleToCenter" aria-label="回到中心点">
            <el-icon><Aim /></el-icon>
          </button>
        </el-tooltip>

        <span class="dock-sep"></span>

        <!-- 垂直布局 -->
        <el-tooltip content="垂直布局" placement="bottom" :show-after="300">
          <button
            class="dock-btn"
            :class="{ active: isActive('treeTB') }"
            @click="handleFab('treeTB')"
            aria-label="垂直布局"
          >
            <el-icon><Connection /></el-icon>
          </button>
        </el-tooltip>

        <!-- 生态树布局 -->
        <el-tooltip content="生态树布局" placement="bottom" :show-after="300">
          <button
            class="dock-btn"
            :class="{ active: isActive('treeLR') }"
            @click="handleFab('treeLR')"
            aria-label="生态树布局"
          >
            <el-icon><Rank /></el-icon>
          </button>
        </el-tooltip>

        <!-- 同心圆 -->
        <el-tooltip content="生态图布局（同心圆）" placement="bottom" :show-after="300">
          <button
            class="dock-btn"
            :class="{ active: isActive('radial') }"
            @click="handleFab('radial')"
            aria-label="生态图布局（同心圆）"
          >
            <el-icon><PieChart /></el-icon>
          </button>
        </el-tooltip>

        <span class="dock-sep"></span>

        <!-- 全屏切换 -->
        <el-tooltip :content="isFullscreen ? '退出全屏' : '进入全屏'" placement="bottom" :show-after="300">
          <button class="dock-btn" @click.stop="toggleFullscreen" aria-label="全屏切换">
            <el-icon><FullScreen /></el-icon>
          </button>
        </el-tooltip>
      </div>
    </div>

    <!-- 右键信息面板 -->
    <div v-if="panelVisible" class="info-panel" @contextmenu.prevent>
      <el-card>
        <div class="panel-hd">
          <div class="title">{{ panel.title }}</div>
          <div class="sub">{{ panel.sub }}</div>
          <el-button class="close" @click="panelVisible = false">×</el-button>
        </div>
        <div class="panel-bd">
          <div class="kv" v-for="(r, i) in attrs" :key="i">
            <div class="k">{{ r.name ?? r.code }}</div>
            <div class="v">{{ r.value }}</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, reactive, watch, nextTick } from "vue";
import cytoscape from "cytoscape";
import { Aim, Connection, Rank, PieChart, FullScreen } from "@element-plus/icons-vue";

/* -------------------- Props & emits -------------------- */
const props = defineProps({
  data: { type: Object, default: () => ({}) },
  attrs: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(["update:panel", "load-children"]);

/* -------------------- State -------------------- */
const containerEl = ref(null);
let cy = null;
const isDragging = ref(false);
const currentLayoutKey = ref("radial"); // 'radial' | 'treeTB' | 'treeLR'
const lastSig = ref(null);

const panelVisible = defineModel("panelVisible", { type: Boolean, default: false });
const panel = reactive({
  title: "",
  sub: "",
});
const isActive = (k) => currentLayoutKey.value === k;

// === 全屏相关（新增） ===
const wrapEl = ref(null);
const isFullscreen = ref(false);

async function enterFullscreen() {
  const el = wrapEl.value;
  if (!el) return;
  if (el.requestFullscreen) await el.requestFullscreen();
  else if (el.webkitRequestFullscreen) el.webkitRequestFullscreen(); // Safari
  else if (el.msRequestFullscreen) el.msRequestFullscreen(); // IE/Edge 旧版
}

async function exitFullscreen() {
  if (document.exitFullscreen) await document.exitFullscreen();
  else if (document.webkitExitFullscreen) document.webkitExitFullscreen(); // Safari
  else if (document.msExitFullscreen) document.msExitFullscreen();
}

async function toggleFullscreen() {
  if (isFullscreen.value) await exitFullscreen();
  else await enterFullscreen();
}

// 进入/退出全屏时的回调：更新状态 & 重新自适应
function onFsChange() {
  const fsEl = document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
  isFullscreen.value = !!fsEl;

  // 尺寸变化后，调用 resize + 重新布局 + 适配视图
  if (cy) {
    requestAnimationFrame(() => {
      try {
        cy.resize();
        cy.fit(undefined, 40);
      } catch {}
    });
  }
}

/* -------------------- UI/交互参数 -------------------- */
const UI = {
  nodeSize: 46, // 节点直径
  fontSize: 12, // 标签字号
  labelMaxWidth: 160, // 标签最大宽度
};
const opts = {
  promoteSingleRoot: false,
  ringGap: 70, // ★ 分量内部“层与层”间距（半径递增步长）
  minNodeSpacing: 40, // ★ 同一圈相邻节点最小间距（像素）
  packGap: 60, // 分量与分量之间的环间距
  compScale: 1.0, // ★ 分量整体放大系数（>1 更松）
};
const NODE_RADIUS = UI.nodeSize / 2;

// 动态获取 concentric 参数（让最新的 opts 生效）
function getConcentricOpts(minSpacingOverride) {
  return {
    fit: false,
    animate: false,
    startAngle: 1.5 * Math.PI,
    clockwise: false,
    minNodeSpacing: minSpacingOverride ?? opts.minNodeSpacing, // ★
    equidistant: true,
    concentric: (node) => -Number(node.data("level") ?? 0),
    levelWidth: () => 1,
    spacingFactor: Math.max(0.5, opts.ringGap / 80), // <1 更紧凑，>=1 更稀
    padding: 12,
  };
}

/* -------------------- 颜色映射 -------------------- */
// 按类型定义的亮眼颜色（高饱和度）
// 按类型定义的颜色（浅色背景+深色边框，适配黑色文字）
const COLOR_BY_TYPE = {
  unit: { fill: "#FFF380", stroke: "#FFC107" }, // 亮黄色背景+深黄边框
  person: { fill: "#80D8FF", stroke: "#03A9F4" }, // 亮蓝色背景+深蓝边框
  train: { fill: "#A2F78D", stroke: "#4CAF50" }, // 亮绿色背景+深绿边框
  other: { fill: "#FFCDD2", stroke: "#F44336" }, // 浅红色背景+深红边框
};

// 扩展调色板（均为高亮度浅色，确保黑色文字清晰）
const PALETTE = [
  { fill: "#FFF380", stroke: "#FFC107" }, // 黄色系
  { fill: "#80D8FF", stroke: "#03A9F4" }, // 蓝色系
  { fill: "#A2F78D", stroke: "#4CAF50" }, // 绿色系
  { fill: "#FFD1DC", stroke: "#E91E63" }, // 粉色系
  { fill: "#FFE0B2", stroke: "#FF9800" }, // 橙色系
  { fill: "#E1BEE7", stroke: "#9C27B0" }, // 紫色系
  { fill: "#B2EBF2", stroke: "#00BCD4" }, // 青色系
  { fill: "#C8E6C9", stroke: "#8BC34A" }, // 淡绿色系
  { fill: "#D1C4E9", stroke: "#673AB7" }, // 淡紫色系
  { fill: "#FFECB3", stroke: "#FFC107" }, // 淡黄色系
];

// 在颜色映射配置后添加
/**
 * 调整颜色亮度（正数变亮，负数变暗）
 * @param {string} color - 十六进制颜色（如#FFF380）
 * @param {number} percent - 亮度调整百分比（-100到100）
 * @returns {string} 调整后的十六进制颜色
 */
function adjustColorBrightness(color, percent) {
  // 解析十六进制颜色
  let R = parseInt(color.slice(1, 3), 16);
  let G = parseInt(color.slice(3, 5), 16);
  let B = parseInt(color.slice(5, 7), 16);

  // 调整亮度
  const factor = 1 + percent / 100;
  R = Math.min(255, Math.max(0, R * factor));
  G = Math.min(255, Math.max(0, G * factor));
  B = Math.min(255, Math.max(0, B * factor));

  // 转回十六进制
  return `#${[R, G, B].map((c) => Math.round(c).toString(16).padStart(2, "0")).join("")}`;
}

function computeColorAuto(std) {
  const types = Array.from(new Set((std.nodes || []).map((n) => (n.data && n.data.type) || "other")));
  const map = {};
  let i = 0;
  for (const t of types) map[t] = COLOR_BY_TYPE[t] || PALETTE[i++ % PALETTE.length];
  return map;
}
let colorMap = {};

/* -------------------- 规范化 std graph -------------------- */
function normalizeGraph(raw) {
  const nodes = raw?.nodes || [];
  const links = raw?.links || [];
  const stdNodes = [];
  const stdEdges = [];
  const idSet = new Set();

  for (const n of nodes) {
    const id = String(n.id);
    if (!id || idSet.has(id)) continue;
    idSet.add(id);
    const label =
      n.nodeName || (n.attrs?.find((a) => a.code === "name" || a.name === "name")?.value ?? "") || n.entityId || id;
    const type = (n?.labels && n.labels[0]) || n?.label || "other";
    stdNodes.push({ id, label, data: { ...n, type } });
  }

  let idx = 0;
  for (const e of links) {
    const s = String(e.source);
    const t = String(e.target);
    if (!idSet.has(s) || !idSet.has(t)) continue;
    stdEdges.push({
      id: e.id ? String(e.id) : `e_${idx++}`,
      source: s,
      target: t,
      label: e.name || "",
      data: { ...e, relation: e.name || "" },
    });
  }
  return { nodes: stdNodes, edges: stdEdges };
}

/* -------------------- 索引/分量/层级 -------------------- */
function buildIndex(std) {
  const und = new Map();
  const out = new Map();
  const indeg = new Map();
  const outdeg = new Map();
  for (const n of std.nodes) {
    und.set(n.id, new Set());
    out.set(n.id, new Set());
    indeg.set(n.id, 0);
    outdeg.set(n.id, 0);
  }
  for (const e of std.edges) {
    und.get(e.source).add(e.target);
    und.get(e.target).add(e.source);
    out.get(e.source).add(e.target);
    indeg.set(e.target, (indeg.get(e.target) || 0) + 1);
    outdeg.set(e.source, (outdeg.get(e.source) || 0) + 1);
  }
  return { und, out, indeg, outdeg };
}
function connectedComponents(std, und) {
  const seen = new Set();
  const comps = [];
  for (const n of std.nodes) {
    if (seen.has(n.id)) continue;
    const comp = [];
    const q = [n.id];
    seen.add(n.id);
    while (q.length) {
      const u = q.shift();
      comp.push(u);
      for (const v of und.get(u) || []) {
        if (!seen.has(v)) {
          seen.add(v);
          q.push(v);
        }
      }
    }
    comps.push(comp);
  }
  return comps;
}
function computeLevels(compIds, out, indeg, outdeg, promoteSingleRoot) {
  const compSet = new Set(compIds);
  let roots = compIds.filter((id) => (indeg.get(id) || 0) === 0);
  const hidden = {};

  if (promoteSingleRoot && roots.length === 1) {
    const r = roots[0];
    const children = Array.from(out.get(r) || []).filter((c) => compSet.has(c));
    if (children.length > 0) {
      roots = children;
      hidden[r] = true;
    }
  }
  if (roots.length === 0 && compIds.length > 0) {
    const pick = compIds.slice().sort((a, b) => outdeg.get(b) - outdeg.get(a))[0];
    roots = [pick];
  }

  const dist = new Map();
  const q = [];
  for (const id of compIds) dist.set(id, Number.POSITIVE_INFINITY);
  for (const r of roots) {
    dist.set(r, 0);
    q.push(r);
  }
  while (q.length) {
    const u = q.shift();
    for (const v of out.get(u) || []) {
      if (!compSet.has(v)) continue;
      const du = dist.get(u);
      if (dist.get(v) > du + 1) {
        dist.set(v, du + 1);
        q.push(v);
      }
    }
  }
  let maxLv = 0;
  const level = {};
  for (const id of compIds) {
    const d = dist.get(id);
    if (Number.isFinite(d)) {
      level[id] = d;
      if (d > maxLv) maxLv = d;
    } else {
      level[id] = maxLv + 1;
    }
  }
  return { level, hidden, maxLevel: maxLv };
}

// ------- 双击判定工具 ------- //
let lastTapId = null;
let lastTapTs = 0;
let lastTapPos = null;
const DBL_DELAY = 280; // 双击时间阈值(ms)
const DBL_DIST = 10; // 双击位置容差(px)

function snapshotViewport() {
  return cy ? { zoom: cy.zoom(), pan: { ...cy.pan() } } : null;
}
function restoreViewport(vp) {
  if (!cy || !vp) return;
  cy.viewport({ zoom: vp.zoom, pan: vp.pan });
}

function isDoubleTap(n, evt) {
  const now = performance.now();
  const id = n.id();
  const p = evt.renderedPosition || { x: 0, y: 0 };
  const nearLast = lastTapPos && Math.hypot(p.x - lastTapPos.x, p.y - lastTapPos.y) <= DBL_DIST;
  const ok = lastTapId === id && now - lastTapTs <= DBL_DELAY && nearLast;

  lastTapId = id;
  lastTapTs = now;
  lastTapPos = p;
  return ok;
}

function normalizeToNode(ele) {
  if (!cy || !ele) return null;

  // 传的是 id 字符串
  if (typeof ele === "string") {
    const col = cy.getElementById(ele);
    return col && col.length ? col[0] : null;
  }

  // 是 cytoscape 的 singular 元素（evt.target 就是这种）
  if (typeof ele.isNode === "function" && ele.isNode()) return ele;

  // 有 id() 方法的其它对象（保险）
  if (typeof ele.id === "function") {
    const col = cy.getElementById(ele.id());
    return col && col.length ? col[0] : null;
  }

  // 可能是 collection
  if (typeof ele.length === "number") {
    return ele.length ? ele[0] : null;
  }

  return null;
}

// 让某个分量做同心圆局部布局；若传入 anchorEl，则以它当前位置为圆心，并锁住它
function relayoutComponentById(componentId, anchorEl = null) {
  if (!cy) return;
  const vp = snapshotViewport();
  cy.startBatch();

  const compNodes = cy.nodes().filter((n) => n.data("componentId") === componentId && !n.data("hidden"));
  const compEdges = cy.edges().filter((e) => compNodes.contains(e.source()) || compNodes.contains(e.target()));
  if (compNodes.empty()) {
    cy.endBatch();
    restoreViewport(vp);
    return;
  }

  // 计算需要的半径（与你 build 阶段一致）
  const countsByLevel = {};
  let maxLevel = 0;
  compNodes.forEach((x) => {
    const lv = Number(x.data("level")) || 0;
    countsByLevel[lv] = (countsByLevel[lv] || 0) + 1;
    if (lv > maxLevel) maxLevel = lv;
  });
  const densest = Math.max(...Object.values(countsByLevel), 1);
  const arcPerNode = UI.nodeSize + opts.minNodeSpacing;
  const needRByCrowding = (densest * arcPerNode) / (2 * Math.PI) + 6;
  const innerPad = 24;
  const needRByDepth = innerPad + maxLevel * opts.ringGap + UI.nodeSize / 2;
  const Rc = Math.max(needRByDepth, needRByCrowding) * opts.compScale;

  // 规范化锚点
  const anchorNode = normalizeToNode(anchorEl);

  // 圆心：优先用锚点（父节点）当前位置；否则用分量包围盒中心
  let cx, cyc;
  if (anchorNode) {
    const p = anchorNode.position();
    cx = p.x;
    cyc = p.y;
    anchorNode.lock(); // ★ 锁住父节点，布局不会改它位置
  } else {
    const bb = compNodes.boundingBox({ includeLabels: false });
    cx = (bb.x1 + bb.x2) / 2;
    cyc = (bb.y1 + bb.y2) / 2;
  }

  const x1 = cx - Rc,
    y1 = cyc - Rc,
    x2 = cx + Rc,
    y2 = cyc + Rc;

  compNodes
    .union(compEdges)
    .layout({
      name: "concentric",
      boundingBox: { x1, y1, x2, y2 },
      ...getConcentricOpts(),
    })
    .run();

  if (anchorNode) anchorNode.unlock(); // ★ 记得解锁

  cy.endBatch();
  restoreViewport(vp); // ★ 视图完全不动
}

// ------- 折叠/展开（父节点） ------- //
function toggleChildren(n) {
  const collapsed = !!n.data("collapsed");
  const out = n.outgoers();
  const childNodes = out.nodes();
  const childEdges = out.edges();

  const vp = snapshotViewport(); // 保险：即使中间有渲染也不动
  cy.startBatch();
  if (!collapsed) {
    childNodes.forEach((x) => x.data("hidden", true));
    childEdges.forEach((x) => x.data("hidden", true));
    n.data("collapsed", true);
  } else {
    childNodes.forEach((x) => x.data("hidden", false));
    childEdges.forEach((x) => x.data("hidden", false));
    n.data("collapsed", false);
  }
  cy.endBatch();

  relayoutComponentById(n.data("componentId")); // ★ 只排该分量
  restoreViewport(vp);
}

// ------- 双击时的主逻辑 ------- //
function handleNodeDblTap(n) {
  if (n.outdegree() > 0) {
    toggleChildren(n); // 父节点：局部隐藏/显示 + 局部布局
  } else {
    // 叶子：让父组件拉数据；父组件拿到数据后调用 graphRef.value.applyChildren(id, {nodes, links})
    emit("load-children", { id: n.id(), raw: n.data("raw") || {} });
  }
}

/**
 * 检查节点是否存在
 */
function existsNode(id) {
  return !cy.getElementById(String(id)).empty();
}
/**
 * 按签名查找边
 */
function findEdgesBySig(sid, tid, rel = "") {
  // 先按两端筛，再比 relation
  const coll = cy.edges(`[source = "${sid}"][target = "${tid}"]`);
  return coll.filter((e) => (e.data("relation") || "") === (rel || ""));
}
/**
 * 按签名生成边 ID
 */
function makeEdgeId(sid, tid, rel = "") {
  return `e_${sid}__${String(rel).replace(/[^\w-]/g, "_")}__${tid}`;
}

// 父组件拿到接口数据后调用：graphRef.value.applyChildren(parentId, {nodes, links})
function applyChildren(parentId, payload) {
  if (!cy) return;
  const parent = cy.getElementById(String(parentId));
  if (parent.empty()) return;

  const compId = parent.data("componentId");
  const parentLevel = Number(parent.data("level")) || 0;
  const pPos = { ...parent.position() };

  const vp = snapshotViewport();
  cy.startBatch();

  // 1) 节点：复用或新增（新增先放到父节点位置）
  for (const nd of payload?.nodes || []) {
    const id = String(nd.id);
    if (!id) continue;

    const label =
      nd.nodeName || (nd.attrs?.find((a) => a.code === "name" || a.name === "name")?.value ?? "") || nd.entityId || id;
    const type = (nd?.labels && nd.labels[0]) || nd?.label || "other";

    if (existsNode(id)) {
      const node = cy.getElementById(id);
      node.data({
        ...node.data(),
        raw: nd,
        type,
        hidden: false,
        componentId: compId,
        level: parentLevel + 1,
      });
    } else {
      cy.add({
        group: "nodes",
        data: {
          id,
          label,
          type,
          raw: nd,
          componentId: compId,
          level: parentLevel + 1,
          hidden: false,
        },
        position: pPos, // 先贴在父节点位置，随后局部布局
      });
    }
  }

  // 2) 边：用 (s,t,rel) 做签名去重；已存在则仅取消隐藏
  for (const e of payload?.links || []) {
    const sid = String(e.source);
    const tid = String(e.target);
    const rel = e.name || "";

    if (cy.getElementById(sid).empty() || cy.getElementById(tid).empty()) continue;

    const dup = findEdgesBySig(sid, tid, rel);
    if (dup.nonempty ? dup.nonempty() : dup.length > 0) {
      dup.forEach((ed) => ed.data("hidden", false));
    } else {
      const eid = e.id ? String(e.id) : makeEdgeId(sid, tid, rel);
      if (!cy.getElementById(eid).empty()) continue; // 同 id 已有
      cy.add({
        group: "edges",
        data: { id: eid, source: sid, target: tid, relation: rel, raw: e, hidden: false },
      });
    }
  }

  cy.endBatch();

  // 3) 只对该分量做局部布局，且把 parent 作为锚点固定
  relayoutComponentById(compId, parent);
  restoreViewport(vp);
}

/* -------------------- 转 elements -------------------- */
function toElements(std, per) {
  const eles = [];
  for (const n of std.nodes) {
    const extra = per[n.id] || {};
    eles.push({ data: { id: n.id, label: n.label || n.id, ...extra } });
  }
  for (const e of std.edges) {
    eles.push({
      data: {
        id: e.id,
        source: e.source,
        target: e.target,
        relation: (e.data && e.data.relation) || e.label || "",
        raw: e.data,
      },
    });
  }
  return eles;
}

/* -------------------- 尺寸/监听 -------------------- */
async function waitContainerReady(maxTries = 30) {
  let tries = 0;
  while (tries++ < maxTries) {
    await new Promise((r) => requestAnimationFrame(r));
    const el = containerEl.value;
    if (!el) continue;
    const { width, height } = el.getBoundingClientRect();
    const cs = window.getComputedStyle(el);
    const ok = width > 20 && height > 20 && cs.display !== "none" && cs.visibility !== "hidden";
    if (ok) return true;
  }
  return false;
}
let ro = null;
function attachResizeObserver() {
  detachResizeObserver();
  if (!containerEl.value || !cy) return;
  ro = new ResizeObserver(() => {
    if (!cy || !containerEl.value) return;
    runLayout(currentLayoutKey.value);
  });
  ro.observe(containerEl.value);
}
function detachResizeObserver() {
  try {
    ro?.disconnect?.();
  } catch {}
  ro = null;
}

/* -------------------- 构建/更新图 -------------------- */
let compMetas = []; // { id, maxLevel, Rc }
async function buildOrUpdateGraph(raw, rebuild = false) {
  const ok = await waitContainerReady();
  if (!ok) return;

  const std = normalizeGraph(raw || {});
  colorMap = computeColorAuto(std);

  const { und, out, indeg, outdeg } = buildIndex(std);
  const comps = connectedComponents(std, und);

  compMetas = [];
  const innerPad = 24;
  const levelStep = opts.ringGap;
  const perNodeMap = {};

  comps.forEach((ids, i) => {
    const { level, hidden, maxLevel } = computeLevels(ids, out, indeg, outdeg, opts.promoteSingleRoot);

    // 标注节点属性
    ids.forEach((id) => {
      const n = std.nodes.find((x) => x.id === id);
      const t = (n && n.data && n.data.type) || "other";
      perNodeMap[id] = { level: level[id], componentId: i, hidden: !!hidden[id], type: t, raw: (n && n.data) || {} };
    });

    // === 关键：按“最拥挤的圈”自动放大分量半径 ===
    const countsByLevel = {};
    ids.forEach((id) => {
      const lv = level[id] || 0;
      countsByLevel[lv] = (countsByLevel[lv] || 0) + 1;
    });
    const densest = Math.max(...Object.values(countsByLevel), 1);
    const arcPerNode = UI.nodeSize + opts.minNodeSpacing; // 每个节点至少占的弧长
    const needCircumference = densest * arcPerNode; // 该圈需要的最小周长
    const needRByCrowding = needCircumference / (2 * Math.PI) + 6; // 折算最小半径
    const needRByDepth = innerPad + maxLevel * levelStep + NODE_RADIUS; // 层数导致的半径
    const Rc = Math.max(needRByDepth, needRByCrowding) * opts.compScale;

    compMetas.push({ id: i, maxLevel, Rc });
  });

  const elements = toElements(std, perNodeMap);

  if (!cy || rebuild) {
    if (cy) {
      cy.destroy();
      cy = null;
    }
    containerEl.value.innerHTML = "";
    cy = cytoscape({
      container: containerEl.value,
      elements,
      pixelRatio: window.devicePixelRatio,
      nodeDimensionsIncludeLabels: true, // ★ 标签尺寸参与碰撞
      style: [
        {
          selector: "node",
          style: {
            label: "data(label)",
            color: "#262626",
            "text-wrap": "wrap",
            "text-max-width": UI.labelMaxWidth,
            "text-valign": "center",
            "text-halign": "center",
            "font-size": UI.fontSize,
            width: UI.nodeSize,
            height: UI.nodeSize,
            "border-width": 1.8,
            "border-color": (ele) => (colorMap[ele.data("type")] || COLOR_BY_TYPE.other).stroke,
            "background-color": (ele) => (colorMap[ele.data("type")] || COLOR_BY_TYPE.other).fill,
            display: (ele) => (ele.data("hidden") ? "none" : "element"),
            "z-index": 2,
          },
        },
        {
          selector: "edge",
          style: {
            "curve-style": "bezier",
            width: 1.4,
            "line-color": "#9aa0a6",
            "target-arrow-shape": "triangle",
            "target-arrow-color": "#9aa0a6",
            label: "data(relation)",
            "font-size": 11,
            "text-background-color": "#fff",
            "text-background-opacity": 1, // 关闭文字背景
            "text-background-padding": 2,
            "z-index": 1,
            display: (ele) => (ele.data("hidden") ? "none" : "element"), // ★ 新增
          },
        },
        { selector: ".faded", style: { opacity: 0.1 } },
        // 在 cytoscape 样式配置中修改 .highlight 部分
        {
          selector: ".highlight",
          style: {
            "border-width": 2.5,
            // 动态获取节点原始边框色并加深
            "border-color": (ele) => {
              const originalStroke = (colorMap[ele.data("type")] || COLOR_BY_TYPE.other).stroke;
              return adjustColorBrightness(originalStroke, -20); // 变暗20%作为高亮边框
            },
            // 动态获取节点原始填充色并调亮
            "background-color": (ele) => {
              const originalFill = (colorMap[ele.data("type")] || COLOR_BY_TYPE.other).fill;
              return adjustColorBrightness(originalFill, 15); // 变亮15%作为高亮填充
            },
          },
        },
        {
          selector: "edge.highlight",
          style: {
            // 边的颜色与节点边框高亮色保持一致
            "line-color": (ele) => {
              const sourceNode = ele.source();
              const originalStroke = (colorMap[sourceNode.data("type")] || COLOR_BY_TYPE.other).stroke;
              return adjustColorBrightness(originalStroke, -20);
            },
            "target-arrow-color": (ele) => {
              const sourceNode = ele.source();
              const originalStroke = (colorMap[sourceNode.data("type")] || COLOR_BY_TYPE.other).stroke;
              return adjustColorBrightness(originalStroke, -20);
            },
            width: 2.2,
          },
        },
      ],
      minZoom: 0.15,
      maxZoom: 6,
    });

    // 交互
    cy.on("grab", "node", () => (isDragging.value = true));
    cy.on("free", "node", () => (isDragging.value = false));
    // 点击空白区域
    cy.on("tap", (evt) => {
      if (evt.target === cy) {
        cy.elements().removeClass("faded highlight");
        panelVisible.value = false;
      }
    });
    // 点击节点
    cy.on("tap", "node", (evt) => {
      const n = evt.target;
      // ★ 双击优先
      if (isDoubleTap(n, evt)) {
        handleNodeDblTap(n);
        return;
      }
      const hood = n.closedNeighborhood();
      cy.elements().addClass("faded");
      hood.removeClass("faded").addClass("highlight");
      n.removeClass("faded").addClass("highlight");
    });
    //  右键
    cy.on("cxttap", "node", (evt) => {
      const n = evt.target;
      const raw = n.data("raw") || {};
      panel.title = n.data("label") || n.id();
      panel.sub = (raw.labels && raw.labels[0]) || raw.type || "";
      emit("update:panel", raw);
    });

    // 右击边
    cy.on("cxttap", "edge", (evt) => {
      const n = evt.target;
      const raw = n.data("raw") || {};
      panel.title = raw.sourceName + " -> " + raw.targetName;
      panel.sub = n.data("relation");
      emit("update:panel", raw);
    });

    attachResizeObserver();
  } else {
    cy.elements().remove();
    cy.add(elements);
  }

  await nextTick();
  runLayout(currentLayoutKey.value);
}

/* -------------------- 分量级同心环拼排（radial） + 树模式 -------------------- */
function runLayout(mode = "radial") {
  if (!cy || !containerEl.value) return;

  // 分量列表（按所需圆半径从大到小排序）
  const metas = compMetas.slice().sort((a, b) => b.Rc - a.Rc);
  if (metas.length === 0) return;

  const { width: W, height: H } = containerEl.value.getBoundingClientRect();
  const CX = W / 2,
    CY = H / 2;

  // 最大分量居中
  const centerMeta = metas.shift();
  placeComponent(centerMeta, CX, CY, centerMeta.Rc, mode);

  // 其余分量分多环等角排布
  const rings = [];
  let currentR = Math.max(280, (centerMeta.Rc || 200) + (metas[0]?.Rc || 160) + opts.packGap);
  rings.push({ R: currentR, items: [] });
  let ringIdx = 0;

  const avgRc = metas.length ? metas.reduce((s, m) => s + m.Rc, 0) / metas.length : 160;
  const tileW = Math.max(2 * avgRc, 180);
  const capacity = (R) => Math.max(6, Math.floor((2 * Math.PI * R) / tileW));

  metas.forEach((m) => {
    const ring = rings[ringIdx];
    if (ring.items.length >= capacity(ring.R)) {
      const maxRcPrev = Math.max(...ring.items.map((x) => x.Rc).concat([avgRc]));
      currentR = ring.R + 2 * maxRcPrev + opts.packGap;
      rings.push({ R: currentR, items: [] });
      ringIdx++;
    }
    rings[ringIdx].items.push(m);
  });

  rings.forEach(({ R, items }) => {
    const n = items.length;
    if (n === 0) return;
    const step = (2 * Math.PI) / n;
    let theta0 = -Math.PI / 2; // 顶部开始
    items.forEach((m, i) => {
      const theta = theta0 + step * i;
      const cx0 = CX + R * Math.cos(theta);
      const cy0 = CY + R * Math.sin(theta);
      placeComponent(m, cx0, cy0, m.Rc, mode);
    });
  });

  cy.fit(undefined, 5);

  // 在以 (cx0, cy0) 为圆心、半径 Rc 的圆盘内为某个分量做布局
  function placeComponent(meta, cx0, cy0, Rc, mode) {
    const nodes = cy.nodes().filter((n) => n.data("componentId") === meta.id);
    const edges = cy.edges().filter((e) => nodes.contains(e.source()) || nodes.contains(e.target()));
    const eles = nodes.union(edges);

    const x1 = cx0 - Rc,
      y1 = cy0 - Rc,
      x2 = cx0 + Rc,
      y2 = cy0 + Rc;

    if (mode === "radial") {
      eles
        .layout({
          name: "concentric",
          boundingBox: { x1, y1, x2, y2 },
          ...getConcentricOpts(), // ★ 使用动态参数
        })
        .run();
      return;
    }

    // 树：breadthfirst 放到同样的圆盘里
    const roots = nodes.filter((n) => Number(n.data("level")) === 0);
    eles
      .layout({
        name: "breadthfirst",
        boundingBox: { x1, y1, x2, y2 },
        fit: false,
        animate: false,
        circle: false,
        directed: true,
        spacingFactor: 1.1, // 可调：更小更紧
        avoidOverlap: true,
        grid: false,
        roots,
      })
      .run();

    // LR：整体旋转 90°
    if (mode === "treeLR") {
      nodes.positions((node) => {
        const p = node.position();
        const dx = p.x - cx0;
        const dy = p.y - cy0;
        return { x: cx0 + dy, y: cy0 - dx };
      });
    }
  }
}

/* -------------------- FAB/辅助 -------------------- */
function handleFab(type) {
  currentLayoutKey.value = type; // 'radial' | 'treeTB' | 'treeLR'
  runLayout(type);
}
function handleToCenter() {
  if (!cy) return;
  cy.fit(undefined, 5);
}
function makeDataSignature(d) {
  const nodes = d?.nodes ?? [];
  const links = d?.links ?? [];
  let h = 0x811c9dc5;
  const mix = (s) => {
    for (let i = 0; i < s.length; i++) {
      h ^= s.charCodeAt(i);
      h = (h >>> 0) * 16777619;
    }
  };
  mix(String(nodes.length));
  for (const n of nodes) mix(`${n.id}|${n.updatedAt ?? n.version ?? ""}`);
  mix(String(links.length));
  for (const e of links) mix(`${e.source}->${e.target}|${e.name ?? ""}`);
  return h >>> 0;
}

/* -------------------- 生命周期 -------------------- */
onMounted(async () => {
  await buildOrUpdateGraph(props.data || {}, true);
  lastSig.value = makeDataSignature(props.data || {});
  document.addEventListener("fullscreenchange", onFsChange);
  document.addEventListener("webkitfullscreenchange", onFsChange);
  document.addEventListener("MSFullscreenChange", onFsChange);
});
onBeforeUnmount(() => {
  detachResizeObserver();
  if (cy) {
    cy.destroy();
    cy = null;
  }
  document.removeEventListener("fullscreenchange", onFsChange);
  document.removeEventListener("webkitfullscreenchange", onFsChange);
  document.removeEventListener("MSFullscreenChange", onFsChange);
});
watch(
  () => props.data,
  async (raw) => {
    const sig = makeDataSignature(raw || {});
    if (sig === lastSig.value) return;
    lastSig.value = sig;
    await buildOrUpdateGraph(raw || {}, false);
  },
  { deep: true, flush: "post" }
);

// 暴露给父组件
defineExpose({ applyChildren });
</script>

<style scoped lang="scss">
.wrap {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
}
.graph {
  width: 100%;
  height: 100%;
  cursor: grab;
}
.graph:active {
  cursor: grabbing;
}

/* 让绝对定位参照图组件容器（你的 .wrap 应该已是 position:relative） */
.tooldock {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 12; /* 比画布高即可 */
}

/* 横向胶囊条 */
.dock {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  border-radius: 999px;
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: saturate(160%) blur(8px);
  border: 1px solid rgba(31, 35, 40, 0.08);
  box-shadow: 0 18px 40px rgba(0, 0, 0, 0.16), 0 2px 10px rgba(0, 0, 0, 0.06);
}

/* 小分隔点 */
.dock-sep {
  width: 1px;
  height: 22px;
  background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.12), transparent);
  margin: 0 2px;
}

/* 图标按钮 */
.dock-btn {
  width: 20px;
  height: 20px;
  display: grid;
  place-items: center;
  border: 0;
  border-radius: 50%;
  background: #ecf5ff; /* 浅蓝底 */
  color: #000;
  cursor: pointer;
  transition: transform 0.08s ease, box-shadow 0.18s ease, background-color 0.18s ease;
}

.dock-btn:hover {
  background: #e8f3ff;
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.25);
  transform: translateY(-1px);
}

.dock-btn:active {
  transform: translateY(0);
}

/* 当前布局高亮 */
.dock-btn.active {
  background: #eef6ff;
  box-shadow: inset 0 0 0 2px #409eff;
}

/* 全屏时抬高层级避免被别的全屏元素覆盖（保险） */
:fullscreen .tooldock,
:-webkit-full-screen .tooldock {
  z-index: 100000;
}

/* 小屏适配：超出时允许横向滚动（可选） */
@media (max-width: 640px) {
  .dock {
    max-width: calc(100vw - 24px);
    overflow-x: auto;
  }
}

/* 信息面板 */
.info-panel {
  position: absolute;
  right: 12px;
  top: 64px;
  width: 340px;
  max-height: calc(100% - 96px);
  background: #111;
  color: #fff;
  border: 1px solid #333;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.35);
  z-index: 12;
}
.panel-hd {
  display: flex;
  align-items: start;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid #222;
}
.title {
  font-weight: 700;
  font-size: 16px;
  flex: 1;
}
.sub {
  color: #aaa;
  font-size: 12px;
  margin-top: 2px;
}
.close {
  background: transparent;
  border: none;
  color: #aaa;
  font-size: 20px;
  cursor: pointer;
}
.panel-bd {
  padding: 10px 12px;
  max-height: 520px;
  overflow: auto;
}
.kv {
  display: grid;
  grid-template-columns: 110px 1fr;
  gap: 6px 10px;
  padding: 6px 0;
  border-bottom: 1px dashed #222;
}
.k {
  color: #409eff;
  word-break: break-all;
}
.v {
  color: #222;
  word-break: break-all;
}
</style>
