<template>
  <div class="player-wrap">
    <UavGimbalWidget
      anchor="bottom-left"
      :offset="{ left: 18, bottom: 18 }"
      :size="160"
      :sendHz="20"
      :maxYaw="120"
      :maxPitch="90"
      @gimbal="onGimbal"
      @zoom="onZoom"
      @action="onAction"
    />
  </div>
</template>

<script setup>
import UavGimbalWidget from "./components/UavGimbalWidget.vue";
function onGimbal({ yaw, pitch }) {
  // TODO: 这里改成你的下发接口调用
  // ws.send(JSON.stringify({ type: 'gimbal', yaw, pitch }))
}
function onZoom({ zoom }) {
  // ws.send(JSON.stringify({ type: 'zoom', zoom }))
}
function onAction(key) {
  // 'video'|'photo'|'display'|'stream'|'settings'|'stabilize'|'grid'|'osd'|'record'|'download'|'more'
}
</script>

<style>
.player-wrap {
  position: relative;
}
</style>
