// 通用"滚轮缩放 + 拖拽平移 + 双指缩放"指令（Vue 3）
// 用法：app.use(panZoom) 后，<div v-pan-zoom="options">...</div>
export default {
  install(app) {
    app.directive('panZoom', {
      mounted(el, binding) {
        const opt = binding?.value || {}

        // 解析目标内容：优先使用 opt.content（HTMLElement / 选择器 / 函数）
        const resolveContent = () => {
          if (opt.content instanceof HTMLElement) return opt.content
          if (typeof opt.content === 'function') return opt.content()
          if (typeof opt.content === 'string') return el.querySelector(opt.content)
          return null
        }

        // 如果未指定内容，则自动包一层 wrapper 承载 transform，避免缩放 div 自身影响布局
        let content = resolveContent()
        let autoWrapped = false
        if (!content) {
          const wrapper = document.createElement('div')
          wrapper.className = 'v-pan-zoom__content'
          // 把 el 的所有子节点移到 wrapper 里
          while (el.firstChild) wrapper.appendChild(el.firstChild)
          el.appendChild(wrapper)
          content = wrapper
          autoWrapped = true
        }

        // 容器样式（承载事件）
        const elCS = getComputedStyle(el)
        if (elCS.position === 'static') el.style.position = 'relative'
        el.style.overflow = opt.overflow ?? 'hidden'
        el.style.touchAction = 'none' // 允许指针事件管理触控

        // 内容样式（承载变换）
        content.style.transformOrigin = '0 0'
        content.style.willChange = 'transform'
        content.style.userSelect = 'none'

        // 状态
        const state = {
          scale: Number.isFinite(opt.initialScale) ? opt.initialScale : 1,
          minScale: Number.isFinite(opt.minScale) ? opt.minScale : 0.2,
          maxScale: Number.isFinite(opt.maxScale) ? opt.maxScale : 8,
          zoomStep: Number.isFinite(opt.zoomStep) ? opt.zoomStep : 0.12,
          tx: 0, ty: 0,
        }

        // 工具
        const clamp = (v, a, b) => Math.min(Math.max(v, a), b)
        const setTransform = () => {
          content.style.transform = `translate(${state.tx}px, ${state.ty}px) scale(${state.scale})`
        }
        const getContainerRect = () => el.getBoundingClientRect()
        const getContentSize = () => {
          // 尽量用滚动尺寸作为"原始尺寸"
          const w = content.scrollWidth || content.clientWidth || content.getBoundingClientRect().width
          const h = content.scrollHeight || content.clientHeight || content.getBoundingClientRect().height
          return { w, h }
        }

        // 边界约束：尽量不让内容完全跑出容器（可关闭：opt.boundary === 'none'）
        const applyBounds = () => {
          if (opt.boundary === 'none') return
          const { w, h } = getContentSize()
          const { width: cw, height: ch } = getContainerRect()
          const scaledW = w * state.scale
          const scaledH = h * state.scale

          // 当内容比容器小：居中（或允许自由移动可改为不限制）
          if (scaledW <= cw) {
            state.tx = (cw - scaledW) / 2
          } else {
            state.tx = clamp(state.tx, cw - scaledW, 0)
          }
          if (scaledH <= ch) {
            state.ty = (ch - scaledH) / 2
          } else {
            state.ty = clamp(state.ty, ch - scaledH, 0)
          }
        }

        // 以鼠标位置为中心缩放（浏览器手感）
        const zoomAt = (clientX, clientY, nextScale) => {
          const rect = getContainerRect()
          const px = clientX - rect.left
          const py = clientY - rect.top

          nextScale = clamp(nextScale, state.minScale, state.maxScale)

          // 变换前光标所在内容坐标
          const cx = (px - state.tx) / state.scale
          const cy = (py - state.ty) / state.scale

          state.scale = nextScale
          // 调整平移，保持该内容点仍在光标下
          state.tx = px - cx * state.scale
          state.ty = py - cy * state.scale

          applyBounds()
          setTransform()
        }

        // ---------- 事件绑定 ----------
        const removeFns = []
        const on = (dom, evt, fn, opts) => {
          dom.addEventListener(evt, fn, opts)
          removeFns.push(() => dom.removeEventListener(evt, fn, opts))
        }

        // 初始化一次
        applyBounds(); setTransform()

        // 滚轮缩放（默认启用；可用 opt.zoomOnWheel=false 关闭；opt.requireCtrl 控制是否需要按住 Ctrl）
        const onWheel = (e) => {
          if (opt.zoomOnWheel === false) return
          if (opt.requireCtrl && !e.ctrlKey) return
          e.preventDefault()
          const dir = e.deltaY > 0 ? -1 : 1 // 向上滚放大
          const factor = 1 + state.zoomStep * dir
          const next = state.scale * factor
          zoomAt(e.clientX, e.clientY, next)
        }
        on(el, 'wheel', onWheel, { passive: false })

        // 拖拽平移（Pointer Events，支持鼠标/触摸/手写笔）
        let dragging = false
        let lastX = 0, lastY = 0

        const onPointerDown = (e) => {
          // 忽略多指：交给 pinch 处理
          if (e.pointerType !== 'mouse' && activePointers.size >= 1) return
          dragging = true
          lastX = e.clientX
          lastY = e.clientY
          content.setPointerCapture?.(e.pointerId)
        }
        const onPointerMove = (e) => {
          // 处理双指缩放
          if (activePointers.size === 2) return pinchMove(e)

          if (!dragging) return
          e.preventDefault()
          const dx = e.clientX - lastX
          const dy = e.clientY - lastY
          lastX = e.clientX
          lastY = e.clientY
          state.tx += dx
          state.ty += dy
          applyBounds()
          setTransform()
        }
        const onPointerUp = (e) => {
          dragging = false
          content.releasePointerCapture?.(e.pointerId)
        }
        on(content, 'pointerdown', onPointerDown)
        on(window, 'pointermove', onPointerMove, { passive: false })
        on(window, 'pointerup', onPointerUp)
        on(window, 'pointercancel', onPointerUp)

        // 双击：默认复位；opt.dblZoomIn=true 则双击放大
        const onDblClick = (e) => {
          if (opt.dblZoomIn) {
            zoomAt(e.clientX, e.clientY, state.scale * (1 + state.zoomStep * 4))
          } else {
            // 复位：居中显示
            state.scale = Number.isFinite(opt.initialScale) ? opt.initialScale : 1
            state.tx = 0; state.ty = 0
            applyBounds(); setTransform()
          }
        }
        if (opt.enableDblClick !== false) on(el, 'dblclick', onDblClick)

        // 双指缩放（Pinch）
        const activePointers = new Map()
        let pinchStart = null // {d, mx, my, scale, tx, ty}
        const getDistance = (a, b) => Math.hypot(a.clientX - b.clientX, a.clientY - b.clientY)
        const getMidpoint = (a, b) => ({ x: (a.clientX + b.clientX) / 2, y: (a.clientY + b.clientY) / 2 })

        const pushPointer = (e) => {
          activePointers.set(e.pointerId, { clientX: e.clientX, clientY: e.clientY })
        }
        const updatePointer = (e) => {
          if (activePointers.has(e.pointerId)) {
            activePointers.set(e.pointerId, { clientX: e.clientX, clientY: e.clientY })
          }
        }
        const removePointer = (e) => {
          activePointers.delete(e.pointerId)
          if (activePointers.size < 2) pinchStart = null
        }

        const pinchStartIfReady = () => {
          if (activePointers.size === 2 && !pinchStart) {
            const [p1, p2] = [...activePointers.values()]
            pinchStart = {
              d: getDistance(p1, p2),
              ...getMidpoint(p1, p2),
              scale: state.scale,
              tx: state.tx,
              ty: state.ty,
            }
          }
        }

        const pinchMove = (e) => {
          updatePointer(e)
          if (activePointers.size !== 2 || !pinchStart) return
          const [p1, p2] = [...activePointers.values()]
          const d = getDistance(p1, p2)
          const mid = getMidpoint(p1, p2)
          const next = clamp(pinchStart.scale * (d / pinchStart.d), state.minScale, state.maxScale)

          // 用中点作为缩放焦点
          const rect = getContainerRect()
          const px = mid.x - rect.left
          const py = mid.y - rect.top
          const cx = (px - pinchStart.tx) / pinchStart.scale
          const cy = (py - pinchStart.ty) / pinchStart.scale

          state.scale = next
          state.tx = px - cx * state.scale
          state.ty = py - cy * state.scale

          applyBounds(); setTransform()
        }

        on(content, 'pointerdown', (e) => {
          pushPointer(e)
          pinchStartIfReady()
        })
        on(window, 'pointermove', (e) => {
          if (activePointers.size === 2) pinchMove(e)
        })
        on(window, 'pointerup', removePointer)
        on(window, 'pointercancel', removePointer)
        on(window, 'pointerout', removePointer)
        on(window, 'pointerleave', removePointer)

        // 公开一些方法（可选）
        el.__panzoom__ = {
          reset() {
            state.scale = Number.isFinite(opt.initialScale) ? opt.initialScale : 1
            state.tx = 0; state.ty = 0
            applyBounds(); setTransform()
          },
          zoomTo(s, center) {
            const rect = getContainerRect()
            const cx = center?.x ?? (rect.left + rect.width / 2)
            const cy = center?.y ?? (rect.top + rect.height / 2)
            zoomAt(cx, cy, s)
          },
          getState() { return { ...state } },
          destroy() {
            removeFns.forEach(fn => fn()); if (autoWrapped) { // 可选：还原包装
              while (content.firstChild) el.appendChild(content.firstChild)
              content.remove()
            }
          },
        }

        // 初始缩放
        if (Number.isFinite(opt.initialScale) && opt.initialScale !== 1) {
          const rect = getContainerRect()
          zoomAt(rect.left + rect.width / 2, rect.top + rect.height / 2, opt.initialScale)
        }
      },

      unmounted(el) {
        el.__panzoom__?.destroy?.()
        delete el.__panzoom__
      },
    })
  },
}
