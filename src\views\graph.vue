<template>
  <div class="h-full w-full">
    <el-button @click="dialogVisible = true">打开</el-button>
    <el-dialog v-model="dialogVisible" width="90%" title="行动详情" append-to-body>
      <div><el-button type="primary">返回</el-button></div>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="action-panel-card" shadow="always">
            <div class="panel-header">
              <span class="panel-title">涉频行动</span>
              <el-tag size="small" effect="plain">{{ currentIndex + 1 }}/{{ actionInfoArr.length }}</el-tag>
            </div>

            <el-carousel
              height="420px"
              :autoplay="false"
              indicator-position="outside"
              arrow="always"
              @change="handleActionChange"
            >
              <el-carousel-item v-for="(item, idx) in actionInfoArr" :key="idx" class="action-item">
                <el-descriptions :column="1" border size="default" class="action-descriptions">
                  <el-descriptions-item v-for="key in FIELD_ORDER" :key="key" :label="ACTION_LABELS[key]">
                    <el-tooltip effect="light" placement="top" :show-after="300" :content="String(item[key] ?? '暂无')">
                      <span class="value-text" :class="key === 'description' ? 'ellipsis-2' : 'ellipsis-1'">
                        {{ item[key] ?? "暂无" }}
                      </span>
                    </el-tooltip>
                  </el-descriptions-item>
                </el-descriptions>
              </el-carousel-item>
            </el-carousel>
          </el-card>
        </el-col>

        <el-col :span="18">
          <div class="graph-host">
            <GraphG6 ref="graphRef" :key="graphKey" :data="data" />
          </div>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, nextTick } from "vue";
import GraphG6 from "@/components/graph/index.vue";
import { cloneDeep } from "lodash";

const dialogVisible = ref(false);
const data = ref({
  nodes: [
    {
      id: "0",
      nodeId: 0,
      label: "军事人员表",
      labels: ["军事人员表"],
      resourceId: "706ac1cf-f970-4064-8a9c-09e77784be5c",
      entityId: "MP-00001",
      nodeName: "张伟",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        {
          code: "_sys_updateTime",
          name: "_sys_updateTime",
          value: "2025-05-26 23:15:14",
          sortId: 0,
          isSystem: true,
        },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "联系方式", name: "联系方式", value: "13800138000", sortId: 0, isSystem: false },
        { code: "人员编号", name: "人员编号", value: "MP-00001", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "706ac1cf-f970-4064-8a9c-09e77784be5c",
          sortId: 0,
          isSystem: false,
        },
        { code: "性别", name: "性别", value: "男", sortId: 0, isSystem: false },
        { code: "紧急联系人", name: "紧急联系人", value: "张建国（父）13100000001", sortId: 0, isSystem: false },
        { code: "姓名", name: "姓名", value: "张伟", sortId: 0, isSystem: false },
        { code: "军衔", name: "军衔", value: "上尉", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@683ffca5", sortId: 0, isSystem: false },
        { code: "出生日期", name: "出生日期", value: "1995-08-12", sortId: 0, isSystem: false },
        { code: "入伍日期", name: "入伍日期", value: "2013-09-01", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "张伟", sortId: 0, isSystem: true },
        { code: "职务", name: "职务", value: "装甲连连长", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "MP-00001", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:21",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "30",
      nodeId: 30,
      label: "军事训练表",
      labels: ["军事训练表"],
      resourceId: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
      entityId: "TR-23001",
      nodeName: "冬季野外生存训练",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "参与人员数量", name: "参与人员数量", value: "120", sortId: 0, isSystem: false },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "开始日期", name: "开始日期", value: "2023-01-05", sortId: 0, isSystem: false },
        { code: "备注", name: "备注", value: "零下30度极限测试", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
          sortId: 0,
          isSystem: false,
        },
        { code: "训练类型", name: "训练类型", value: "野外生存", sortId: 0, isSystem: false },
        { code: "结束日期", name: "结束日期", value: "2023-01-15", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@7ad36add", sortId: 0, isSystem: false },
        { code: "训练结果", name: "训练结果", value: "优秀", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "冬季野外生存训练", sortId: 0, isSystem: true },
        { code: "训练地点", name: "训练地点", value: "昆仑山训练基地", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "TR-23001", sortId: 0, isSystem: true },
        { code: "训练名称", name: "训练名称", value: "冬季野外生存训练", sortId: 0, isSystem: false },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:32",
          sortId: 0,
          isSystem: true,
        },
        { code: "训练编号", name: "训练编号", value: "TR-23001", sortId: 0, isSystem: false },
      ],
    },
    {
      id: "20",
      nodeId: 20,
      label: "军事人员表",
      labels: ["军事人员表"],
      resourceId: "706ac1cf-f970-4064-8a9c-09e77784be5c",
      entityId: "MP-00021",
      nodeName: "方振华",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        {
          code: "_sys_updateTime",
          name: "_sys_updateTime",
          value: "2025-05-26 23:15:14",
          sortId: 0,
          isSystem: true,
        },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "联系方式", name: "联系方式", value: "13802138000", sortId: 0, isSystem: false },
        { code: "人员编号", name: "人员编号", value: "MP-00021", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "706ac1cf-f970-4064-8a9c-09e77784be5c",
          sortId: 0,
          isSystem: false,
        },
        { code: "性别", name: "性别", value: "男", sortId: 0, isSystem: false },
        { code: "紧急联系人", name: "紧急联系人", value: "方玉梅（母）13300000021", sortId: 0, isSystem: false },
        { code: "姓名", name: "姓名", value: "方振华", sortId: 0, isSystem: false },
        { code: "军衔", name: "军衔", value: "上尉", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@75a1818d", sortId: 0, isSystem: false },
        { code: "出生日期", name: "出生日期", value: "1993-02-14", sortId: 0, isSystem: false },
        { code: "入伍日期", name: "入伍日期", value: "2011-04-15", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "方振华", sortId: 0, isSystem: true },
        { code: "职务", name: "职务", value: "侦察排长", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "MP-00021", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:21",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "10",
      nodeId: 10,
      label: "军事人员表",
      labels: ["军事人员表"],
      resourceId: "706ac1cf-f970-4064-8a9c-09e77784be5c",
      entityId: "MP-00011",
      nodeName: "孙志勇",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        {
          code: "_sys_updateTime",
          name: "_sys_updateTime",
          value: "2025-05-26 23:15:14",
          sortId: 0,
          isSystem: true,
        },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "联系方式", name: "联系方式", value: "13801138000", sortId: 0, isSystem: false },
        { code: "人员编号", name: "人员编号", value: "MP-00011", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "706ac1cf-f970-4064-8a9c-09e77784be5c",
          sortId: 0,
          isSystem: false,
        },
        { code: "性别", name: "性别", value: "男", sortId: 0, isSystem: false },
        { code: "紧急联系人", name: "紧急联系人", value: "孙丽华（妻）13200000011", sortId: 0, isSystem: false },
        { code: "姓名", name: "姓名", value: "孙志勇", sortId: 0, isSystem: false },
        { code: "军衔", name: "军衔", value: "大校", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@27af14ff", sortId: 0, isSystem: false },
        { code: "出生日期", name: "出生日期", value: "1991-10-15", sortId: 0, isSystem: false },
        { code: "入伍日期", name: "入伍日期", value: "2009-08-01", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "孙志勇", sortId: 0, isSystem: true },
        { code: "职务", name: "职务", value: "旅参谋长", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "MP-00011", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:21",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "47",
      nodeId: 47,
      label: "军事训练表",
      labels: ["军事训练表"],
      resourceId: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
      entityId: "TR-23018",
      nodeName: "装甲维修技能大赛",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "参与人员数量", name: "参与人员数量", value: "50", sortId: 0, isSystem: false },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "开始日期", name: "开始日期", value: "2023-09-18", sortId: 0, isSystem: false },
        { code: "备注", name: "备注", value: "故障修复用时破纪录", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
          sortId: 0,
          isSystem: false,
        },
        { code: "训练类型", name: "训练类型", value: "技术比武", sortId: 0, isSystem: false },
        { code: "结束日期", name: "结束日期", value: "2023-09-20", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@4ea0bbb", sortId: 0, isSystem: false },
        { code: "训练结果", name: "训练结果", value: "卓越", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "装甲维修技能大赛", sortId: 0, isSystem: true },
        { code: "训练地点", name: "训练地点", value: "装甲兵工程学院", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "TR-23018", sortId: 0, isSystem: true },
        { code: "训练名称", name: "训练名称", value: "装甲维修技能大赛", sortId: 0, isSystem: false },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:32",
          sortId: 0,
          isSystem: true,
        },
        { code: "训练编号", name: "训练编号", value: "TR-23018", sortId: 0, isSystem: false },
      ],
    },
    {
      id: "56",
      nodeId: 56,
      label: "军事训练表",
      labels: ["军事训练表"],
      resourceId: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
      entityId: "TR-23027",
      nodeName: "伪装侦察特训",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "参与人员数量", name: "参与人员数量", value: "35", sortId: 0, isSystem: false },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "开始日期", name: "开始日期", value: "2023-09-25", sortId: 0, isSystem: false },
        { code: "备注", name: "备注", value: "热成像对抗", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
          sortId: 0,
          isSystem: false,
        },
        { code: "训练类型", name: "训练类型", value: "侦察训练", sortId: 0, isSystem: false },
        { code: "结束日期", name: "结束日期", value: "2023-09-27", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@bc19880", sortId: 0, isSystem: false },
        { code: "训练结果", name: "训练结果", value: "良好", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "伪装侦察特训", sortId: 0, isSystem: true },
        { code: "训练地点", name: "训练地点", value: "多地形训练场", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "TR-23027", sortId: 0, isSystem: true },
        { code: "训练名称", name: "训练名称", value: "伪装侦察特训", sortId: 0, isSystem: false },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:32",
          sortId: 0,
          isSystem: true,
        },
        { code: "训练编号", name: "训练编号", value: "TR-23027", sortId: 0, isSystem: false },
      ],
    },
    {
      id: "76",
      nodeId: 76,
      label: "军事作战单位表",
      labels: ["军事作战单位表"],
      resourceId: "fd0fd40e-d289-4b1f-9e1e-83c0258091d8",
      entityId: "UN-001",
      nodeName: "第1装甲师",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "驻地", name: "驻地", value: "华北平原", sortId: 0, isSystem: false },
        { code: "装备数量", name: "装备数量", value: "420", sortId: 0, isSystem: false },
        { code: "指挥官编号", name: "指挥官编号", value: "MP-00013", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "fd0fd40e-d289-4b1f-9e1e-83c0258091d8",
          sortId: 0,
          isSystem: false,
        },
        { code: "单位名称", name: "单位名称", value: "第1装甲师", sortId: 0, isSystem: false },
        { code: "单位类型", name: "单位类型", value: "陆军", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@2070dfec", sortId: 0, isSystem: false },
        { code: "编制人数", name: "编制人数", value: "8500", sortId: 0, isSystem: false },
        { code: "作战状态", name: "作战状态", value: "全面就绪", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "第1装甲师", sortId: 0, isSystem: true },
        { code: "成立日期", name: "成立日期", value: "1970-01-01", sortId: 0, isSystem: false },
        { code: "上级单位编号", name: "上级单位编号", value: "", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "UN-001", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 11:51:44",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
  ],
  links: [
    {
      id: "39",
      linkId: 39,
      name: "参与训练",
      source: "0",
      target: "30",
      sourceName: "张伟",
      targetName: "冬季野外生存训练",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "45",
      linkId: 45,
      name: "参与训练",
      source: "20",
      target: "30",
      sourceName: "方振华",
      targetName: "冬季野外生存训练",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "42",
      linkId: 42,
      name: "参与训练",
      source: "10",
      target: "30",
      sourceName: "孙志勇",
      targetName: "冬季野外生存训练",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "40",
      linkId: 40,
      name: "参与训练",
      source: "0",
      target: "47",
      sourceName: "张伟",
      targetName: "装甲维修技能大赛",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "46",
      linkId: 46,
      name: "参与训练",
      source: "20",
      target: "47",
      sourceName: "方振华",
      targetName: "装甲维修技能大赛",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "43",
      linkId: 43,
      name: "参与训练",
      source: "10",
      target: "47",
      sourceName: "孙志勇",
      targetName: "装甲维修技能大赛",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "41",
      linkId: 41,
      name: "参与训练",
      source: "0",
      target: "56",
      sourceName: "张伟",
      targetName: "伪装侦察特训",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "44",
      linkId: 44,
      name: "参与训练",
      source: "10",
      target: "56",
      sourceName: "孙志勇",
      targetName: "伪装侦察特训",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "47",
      linkId: 47,
      name: "参与训练",
      source: "20",
      target: "56",
      sourceName: "方振华",
      targetName: "伪装侦察特训",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "192",
      linkId: 192,
      name: "隶属关系",
      source: "76",
      target: "0",
      sourceName: "第1装甲师",
      targetName: "张伟",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 15:09:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "5a715319-d9f6-40d2-8a62-59963a80733e",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "194",
      linkId: 194,
      name: "隶属关系",
      source: "76",
      target: "20",
      sourceName: "第1装甲师",
      targetName: "方振华",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 15:09:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "5a715319-d9f6-40d2-8a62-59963a80733e",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "193",
      linkId: 193,
      name: "隶属关系",
      source: "76",
      target: "10",
      sourceName: "第1装甲师",
      targetName: "孙志勇",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 15:09:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "5a715319-d9f6-40d2-8a62-59963a80733e",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
  ],
  renderType: null,
  columns: null,
  rows: null,
});

const data1 = {
  nodes: [
    {
      id: "0",
      nodeId: 0,
      label: "军事人员表",
      labels: ["军事人员表"],
      resourceId: "706ac1cf-f970-4064-8a9c-09e77784be5c",
      entityId: "MP-00001",
      nodeName: "张伟",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        {
          code: "_sys_updateTime",
          name: "_sys_updateTime",
          value: "2025-05-26 23:15:14",
          sortId: 0,
          isSystem: true,
        },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "联系方式", name: "联系方式", value: "13800138000", sortId: 0, isSystem: false },
        { code: "人员编号", name: "人员编号", value: "MP-00001", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "706ac1cf-f970-4064-8a9c-09e77784be5c",
          sortId: 0,
          isSystem: false,
        },
        { code: "性别", name: "性别", value: "男", sortId: 0, isSystem: false },
        { code: "紧急联系人", name: "紧急联系人", value: "张建国（父）13100000001", sortId: 0, isSystem: false },
        { code: "姓名", name: "姓名", value: "张伟", sortId: 0, isSystem: false },
        { code: "军衔", name: "军衔", value: "上尉", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@683ffca5", sortId: 0, isSystem: false },
        { code: "出生日期", name: "出生日期", value: "1995-08-12", sortId: 0, isSystem: false },
        { code: "入伍日期", name: "入伍日期", value: "2013-09-01", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "张伟", sortId: 0, isSystem: true },
        { code: "职务", name: "职务", value: "装甲连连长", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "MP-00001", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:21",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "30",
      nodeId: 30,
      label: "军事训练表",
      labels: ["军事训练表"],
      resourceId: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
      entityId: "TR-23001",
      nodeName: "冬季野外生存训练",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "参与人员数量", name: "参与人员数量", value: "120", sortId: 0, isSystem: false },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "开始日期", name: "开始日期", value: "2023-01-05", sortId: 0, isSystem: false },
        { code: "备注", name: "备注", value: "零下30度极限测试", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
          sortId: 0,
          isSystem: false,
        },
        { code: "训练类型", name: "训练类型", value: "野外生存", sortId: 0, isSystem: false },
        { code: "结束日期", name: "结束日期", value: "2023-01-15", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@7ad36add", sortId: 0, isSystem: false },
        { code: "训练结果", name: "训练结果", value: "优秀", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "冬季野外生存训练", sortId: 0, isSystem: true },
        { code: "训练地点", name: "训练地点", value: "昆仑山训练基地", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "TR-23001", sortId: 0, isSystem: true },
        { code: "训练名称", name: "训练名称", value: "冬季野外生存训练", sortId: 0, isSystem: false },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:32",
          sortId: 0,
          isSystem: true,
        },
        { code: "训练编号", name: "训练编号", value: "TR-23001", sortId: 0, isSystem: false },
      ],
    },
    {
      id: "20",
      nodeId: 20,
      label: "军事人员表",
      labels: ["军事人员表"],
      resourceId: "706ac1cf-f970-4064-8a9c-09e77784be5c",
      entityId: "MP-00021",
      nodeName: "方振华",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        {
          code: "_sys_updateTime",
          name: "_sys_updateTime",
          value: "2025-05-26 23:15:14",
          sortId: 0,
          isSystem: true,
        },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "联系方式", name: "联系方式", value: "13802138000", sortId: 0, isSystem: false },
        { code: "人员编号", name: "人员编号", value: "MP-00021", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "706ac1cf-f970-4064-8a9c-09e77784be5c",
          sortId: 0,
          isSystem: false,
        },
        { code: "性别", name: "性别", value: "男", sortId: 0, isSystem: false },
        { code: "紧急联系人", name: "紧急联系人", value: "方玉梅（母）13300000021", sortId: 0, isSystem: false },
        { code: "姓名", name: "姓名", value: "方振华", sortId: 0, isSystem: false },
        { code: "军衔", name: "军衔", value: "上尉", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@75a1818d", sortId: 0, isSystem: false },
        { code: "出生日期", name: "出生日期", value: "1993-02-14", sortId: 0, isSystem: false },
        { code: "入伍日期", name: "入伍日期", value: "2011-04-15", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "方振华", sortId: 0, isSystem: true },
        { code: "职务", name: "职务", value: "侦察排长", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "MP-00021", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:21",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "10",
      nodeId: 10,
      label: "军事人员表",
      labels: ["军事人员表"],
      resourceId: "706ac1cf-f970-4064-8a9c-09e77784be5c",
      entityId: "MP-00011",
      nodeName: "孙志勇",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        {
          code: "_sys_updateTime",
          name: "_sys_updateTime",
          value: "2025-05-26 23:15:14",
          sortId: 0,
          isSystem: true,
        },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "联系方式", name: "联系方式", value: "13801138000", sortId: 0, isSystem: false },
        { code: "人员编号", name: "人员编号", value: "MP-00011", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "706ac1cf-f970-4064-8a9c-09e77784be5c",
          sortId: 0,
          isSystem: false,
        },
        { code: "性别", name: "性别", value: "男", sortId: 0, isSystem: false },
        { code: "紧急联系人", name: "紧急联系人", value: "孙丽华（妻）13200000011", sortId: 0, isSystem: false },
        { code: "姓名", name: "姓名", value: "孙志勇", sortId: 0, isSystem: false },
        { code: "军衔", name: "军衔", value: "大校", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@27af14ff", sortId: 0, isSystem: false },
        { code: "出生日期", name: "出生日期", value: "1991-10-15", sortId: 0, isSystem: false },
        { code: "入伍日期", name: "入伍日期", value: "2009-08-01", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "孙志勇", sortId: 0, isSystem: true },
        { code: "职务", name: "职务", value: "旅参谋长", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "MP-00011", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:21",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "47",
      nodeId: 47,
      label: "军事训练表",
      labels: ["军事训练表"],
      resourceId: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
      entityId: "TR-23018",
      nodeName: "装甲维修技能大赛",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "参与人员数量", name: "参与人员数量", value: "50", sortId: 0, isSystem: false },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "开始日期", name: "开始日期", value: "2023-09-18", sortId: 0, isSystem: false },
        { code: "备注", name: "备注", value: "故障修复用时破纪录", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
          sortId: 0,
          isSystem: false,
        },
        { code: "训练类型", name: "训练类型", value: "技术比武", sortId: 0, isSystem: false },
        { code: "结束日期", name: "结束日期", value: "2023-09-20", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@4ea0bbb", sortId: 0, isSystem: false },
        { code: "训练结果", name: "训练结果", value: "卓越", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "装甲维修技能大赛", sortId: 0, isSystem: true },
        { code: "训练地点", name: "训练地点", value: "装甲兵工程学院", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "TR-23018", sortId: 0, isSystem: true },
        { code: "训练名称", name: "训练名称", value: "装甲维修技能大赛", sortId: 0, isSystem: false },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:32",
          sortId: 0,
          isSystem: true,
        },
        { code: "训练编号", name: "训练编号", value: "TR-23018", sortId: 0, isSystem: false },
      ],
    },
    {
      id: "56",
      nodeId: 56,
      label: "军事训练表",
      labels: ["军事训练表"],
      resourceId: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
      entityId: "TR-23027",
      nodeName: "伪装侦察特训",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "参与人员数量", name: "参与人员数量", value: "35", sortId: 0, isSystem: false },
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "开始日期", name: "开始日期", value: "2023-09-25", sortId: 0, isSystem: false },
        { code: "备注", name: "备注", value: "热成像对抗", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "d3085bec-c06e-4397-a3f3-8cfdf1db01d3",
          sortId: 0,
          isSystem: false,
        },
        { code: "训练类型", name: "训练类型", value: "侦察训练", sortId: 0, isSystem: false },
        { code: "结束日期", name: "结束日期", value: "2023-09-27", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@bc19880", sortId: 0, isSystem: false },
        { code: "训练结果", name: "训练结果", value: "良好", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "伪装侦察特训", sortId: 0, isSystem: true },
        { code: "训练地点", name: "训练地点", value: "多地形训练场", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "TR-23027", sortId: 0, isSystem: true },
        { code: "训练名称", name: "训练名称", value: "伪装侦察特训", sortId: 0, isSystem: false },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-26 22:57:32",
          sortId: 0,
          isSystem: true,
        },
        { code: "训练编号", name: "训练编号", value: "TR-23027", sortId: 0, isSystem: false },
      ],
    },
    {
      id: "76",
      nodeId: 76,
      label: "军事作战单位表",
      labels: ["军事作战单位表"],
      resourceId: "fd0fd40e-d289-4b1f-9e1e-83c0258091d8",
      entityId: "UN-001",
      nodeName: "第1装甲师",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        { code: "单位编号", name: "单位编号", value: "UN-001", sortId: 0, isSystem: false },
        { code: "驻地", name: "驻地", value: "华北平原", sortId: 0, isSystem: false },
        { code: "装备数量", name: "装备数量", value: "420", sortId: 0, isSystem: false },
        { code: "指挥官编号", name: "指挥官编号", value: "MP-00013", sortId: 0, isSystem: false },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "fd0fd40e-d289-4b1f-9e1e-83c0258091d8",
          sortId: 0,
          isSystem: false,
        },
        { code: "单位名称", name: "单位名称", value: "第1装甲师", sortId: 0, isSystem: false },
        { code: "单位类型", name: "单位类型", value: "陆军", sortId: 0, isSystem: false },
        { code: "标签主体系统字段", name: "标签主体系统字段", value: "[I@2070dfec", sortId: 0, isSystem: false },
        { code: "编制人数", name: "编制人数", value: "8500", sortId: 0, isSystem: false },
        { code: "作战状态", name: "作战状态", value: "全面就绪", sortId: 0, isSystem: false },
        { code: "name", name: "name", value: "第1装甲师", sortId: 0, isSystem: true },
        { code: "成立日期", name: "成立日期", value: "1970-01-01", sortId: 0, isSystem: false },
        { code: "上级单位编号", name: "上级单位编号", value: "", sortId: 0, isSystem: false },
        { code: "id", name: "id", value: "UN-001", sortId: 0, isSystem: true },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 11:51:44",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
  ],
  links: [
    {
      id: "39",
      linkId: 39,
      name: "参与训练",
      source: "0",
      target: "30",
      sourceName: "张伟",
      targetName: "冬季野外生存训练",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "45",
      linkId: 45,
      name: "参与训练",
      source: "20",
      target: "30",
      sourceName: "方振华",
      targetName: "冬季野外生存训练",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "42",
      linkId: 42,
      name: "参与训练",
      source: "10",
      target: "30",
      sourceName: "孙志勇",
      targetName: "冬季野外生存训练",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "40",
      linkId: 40,
      name: "参与训练",
      source: "0",
      target: "47",
      sourceName: "张伟",
      targetName: "装甲维修技能大赛",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "46",
      linkId: 46,
      name: "参与训练",
      source: "20",
      target: "47",
      sourceName: "方振华",
      targetName: "装甲维修技能大赛",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "43",
      linkId: 43,
      name: "参与训练",
      source: "10",
      target: "47",
      sourceName: "孙志勇",
      targetName: "装甲维修技能大赛",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "41",
      linkId: 41,
      name: "参与训练",
      source: "0",
      target: "56",
      sourceName: "张伟",
      targetName: "伪装侦察特训",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "44",
      linkId: 44,
      name: "参与训练",
      source: "10",
      target: "56",
      sourceName: "孙志勇",
      targetName: "伪装侦察特训",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "47",
      linkId: 47,
      name: "参与训练",
      source: "20",
      target: "56",
      sourceName: "方振华",
      targetName: "伪装侦察特训",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 00:28:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "634c5464-3ad2-47ff-966c-bed6ad1cea30",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "192",
      linkId: 192,
      name: "隶属关系",
      source: "76",
      target: "0",
      sourceName: "第1装甲师",
      targetName: "张伟",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 15:09:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "5a715319-d9f6-40d2-8a62-59963a80733e",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "194",
      linkId: 194,
      name: "隶属关系",
      source: "76",
      target: "20",
      sourceName: "第1装甲师",
      targetName: "方振华",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 15:09:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "5a715319-d9f6-40d2-8a62-59963a80733e",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
    {
      id: "193",
      linkId: 193,
      name: "隶属关系",
      source: "76",
      target: "10",
      sourceName: "第1装甲师",
      targetName: "孙志勇",
      sourceObject: null,
      targetObject: null,
      lineType: "direct",
      value: 1,
      attrs: [
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-05-27 15:09:04",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "_sys_relationTypeId",
          name: "_sys_relationTypeId",
          value: "5a715319-d9f6-40d2-8a62-59963a80733e",
          sortId: 0,
          isSystem: true,
        },
      ],
    },
  ],
  renderType: null,
  columns: null,
  rows: null,
};

const data2 = {
  nodes: [
    {
      id: "14",
      nodeId: 14,
      label: "业务定义",
      labels: ["业务定义"],
      resourceId: "a39154a7-d137-4d7e-9d64-33c922d26937",
      entityId: "AMR",
      nodeName: "航空移动(R)",
      color: null,
      shape: null,
      icon: null,
      attrs: [
        {
          code: "_sys_updateTime",
          name: "_sys_updateTime",
          value: "2025-08-28 08:05:32",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "业务代码",
          name: "业务代码",
          value: "AMR",
          sortId: 0,
          isSystem: false,
        },
        {
          code: "数据代码",
          name: "数据代码",
          value: "AM",
          sortId: 0,
          isSystem: false,
        },
        {
          code: "业务名称",
          name: "业务名称",
          value: "航空移动(R)",
          sortId: 0,
          isSystem: false,
        },
        {
          code: "originResourceId",
          name: "originResourceId",
          value: "a39154a7-d137-4d7e-9d64-33c922d26937",
          sortId: 0,
          isSystem: false,
        },
        {
          code: "颜色_红",
          name: "颜色_红",
          value: "171",
          sortId: 0,
          isSystem: false,
        },
        {
          code: "安全业务",
          name: "安全业务",
          value: "-",
          sortId: 0,
          isSystem: false,
        },
        {
          code: "业务定义",
          name: "业务定义",
          value: "供主要与沿国内或国际民航航线的飞行安全和飞行正常有关的通信使用的航空移动业务。在此，R为route的缩写。",
          sortId: 0,
          isSystem: false,
        },
        {
          code: "name",
          name: "name",
          value: "航空移动(R)",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "id",
          name: "id",
          value: "AMR",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "颜色_蓝",
          name: "颜色_蓝",
          value: "148",
          sortId: 0,
          isSystem: false,
        },
        {
          code: "_sys_createTime",
          name: "_sys_createTime",
          value: "2025-06-07 15:08:00",
          sortId: 0,
          isSystem: true,
        },
        {
          code: "颜色_绿",
          name: "颜色_绿",
          value: "146",
          sortId: 0,
          isSystem: false,
        },
      ],
    },
  ],
  links: [],
  renderType: null,
  columns: null,
  rows: null,
};

// 固定 label（你要求的“固定的label”）
const ACTION_LABELS = {
  name: "行动名称",
  startTime: "开始时间",
  duration: "任务时长",
  zzfsType: "作战方向",
  position: "区域范围",
  description: "行动描述",
};

// 显示顺序（可按需调整）
const FIELD_ORDER = ["name", "startTime", "duration", "zzfsType", "position", "description"];

// 示例数据（你可替换为真实数据）
const actionInfoArr = ref([
  {
    name: "空域巡逻-东部扇区",
    startTime: "2025-08-29 10:00",
    duration: "02:30:00",
    zzfsType: "东向",
    position: "E123°-E125° / N24°-N26°",
    description: "编队二号航线，重点侦察东南侧活动目标。",
  },
  {
    name: "海域封控-南部走廊",
    startTime: "2025-08-29 14:30",
    duration: "01:45:00",
    zzfsType: "南向",
    position: "E120°-E122° / N22°-N23.5°",
    description: "两点位设卡，联合电子压制，观察频段X变化。",
  },
]);

const currentIndex = ref(0);
const graphKey = ref(0);
let switchTimer = 0;
const DELAY_MS = 500; // 增加延迟时间
const handleActionChange = (i) => {
  window.clearTimeout(switchTimer);
  switchTimer = window.setTimeout(async () => {
    currentIndex.value = i;
    graphKey.value++;
    await new Promise((resolve) => setTimeout(resolve, 50)); // 额外等待
    data.value = cloneDeep(i === 0 ? data1 : data2);
    await nextTick();
  }, DELAY_MS);
};
</script>

<style scoped>
.action-panel-card {
  position: sticky;
  top: 12px;
  /* 让面板在滚动时固定左侧，适合右侧图可滚动的布局 */
  --label-bg: var(--el-color-info-light-9);
  --label-width: 92px;
  --cell-padding: 10px 12px;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.panel-title {
  font-weight: 600;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.action-item {
  padding: 4px 2px;
  overflow: auto;
}

/* 统一控制 label 宽度 */
.action-descriptions :deep(.el-descriptions__table) {
  table-layout: fixed; /* 让 width 生效、更稳 */
}
.action-descriptions :deep(.el-descriptions__cell.is-bordered-label) {
  width: 100px; /* 改这里就能全局调整宽度 */
  text-align: center;
}

/* 让 Descriptions 的内容单元格在容器里可正确压缩，方便省略号生效 */
.action-descriptions :deep(.el-descriptions__content) {
  min-width: 0;
}

/* 通用的值样式，保证宽度占满单元格 */
.value-text {
  display: block;
  width: 100%;
  line-height: 1.5;
  overflow-wrap: anywhere; /* 中文/长串都能优雅换行（tooltip里用） */
}

/* 单行省略 */
.ellipsis-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 多行省略（这里设成2行，按需改为 3/4） */
.ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 可按需要调：建议 70~78vh */
.graph-host {
  position: relative;
  height: 54vh; /* 关键：固定视口高度 */
  min-height: 520px;
}
</style>
