<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import flvjs from "flv.js";

// 响应式状态
const videoRef = ref(null);
const isConnected = ref(false);
const isStreaming = ref(false);
const status = ref("未连接");
const dataFormat = ref("未知");
const playbackMethod = ref("未知");

// WebSocket 和播放器实例
let ws = null;
let player = null;
let mediaSource = null;
let dataBuffer = [];
let isPlayerReady = false;

// 配置
const WS_URL = "ws://***********:9085/ws/video-stream";

// 开始推流
function startStream() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    alert("WebSocket 未连接，请等待连接建立");
    return;
  }

  const command = {
    action: "start",
    streamUrl: "http://devimages.apple.com.edgekey.net/streaming/examples/bipbop_4x3/gear2/prog_index.m3u8",
  };

  ws.send(JSON.stringify(command));
  console.log("发送开始指令:", command);

  // 清空缓冲区
  dataBuffer = [];
  isPlayerReady = false;
  dataFormat.value = "未知";
  playbackMethod.value = "未知";

  isStreaming.value = true;
  status.value = "正在请求流...";
}

// 停止推流
function stopStream() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    alert("WebSocket 未连接");
    return;
  }

  const command = { action: "stop" };
  ws.send(JSON.stringify(command));
  console.log("发送停止指令:", command);

  cleanupPlayer();
  dataBuffer = [];
  isPlayerReady = false;

  isStreaming.value = false;
  status.value = "已停止";
}

// 处理二进制数据
function handleBinaryData(data) {
  console.log("收到二进制数据:", data.byteLength, "字节");

  const uint8Array = new Uint8Array(data);

  // 分析数据格式
  const format = analyzeDataFormat(uint8Array);
  dataFormat.value = format;

  // 将数据添加到缓冲区
  dataBuffer.push(uint8Array);

  // 如果播放器还未准备好，尝试初始化
  if (!isPlayerReady && dataBuffer.length > 0) {
    initializePlayer(format);
  }
}

// 分析数据格式
function analyzeDataFormat(data) {
  if (data.length < 10) {
    return "数据太短";
  }

  const header = Array.from(data.slice(0, 10))
    .map((b) => b.toString(16).padStart(2, "0"))
    .join(" ");
  console.log("数据头部 (hex):", header);

  // 检查 FLV 格式
  if (data[0] === 0x46 && data[1] === 0x4c && data[2] === 0x56) {
    console.log("✅ 检测到 FLV 格式");
    return "FLV";
  }

  // 检查 MP4 格式
  if (data[4] === 0x66 && data[5] === 0x74 && data[6] === 0x79 && data[7] === 0x70) {
    console.log("✅ 检测到 MP4 格式");
    return "MP4";
  }

  // 检查 WebM 格式
  if (data[0] === 0x1a && data[1] === 0x45 && data[2] === 0xdf && data[3] === 0xa3) {
    console.log("✅ 检测到 WebM 格式");
    return "WebM";
  }

  // 检查 H.264 NAL 单元
  if (data[0] === 0x00 && data[1] === 0x00 && data[2] === 0x01) {
    console.log("✅ 检测到 H.264 NAL 单元");
    return "H.264";
  }

  // 检查 MPEG-TS
  if (data[0] === 0x47) {
    console.log("✅ 检测到 MPEG-TS 格式");
    return "MPEG-TS";
  }

  console.log("❌ 未知格式");
  return "未知";
}

// 初始化播放器
function initializePlayer(format) {
  console.log("初始化播放器，格式:", format);

  // 合并所有数据
  const totalLength = dataBuffer.reduce((sum, arr) => sum + arr.length, 0);
  const mergedData = new Uint8Array(totalLength);
  let offset = 0;

  for (const chunk of dataBuffer) {
    mergedData.set(chunk, offset);
    offset += chunk.length;
  }

  console.log("合并数据总长度:", totalLength, "字节");

  // 根据格式选择播放方式
  switch (format) {
    case "FLV":
      tryFlvPlayback(mergedData);
      break;
    case "MP4":
      tryNativePlayback(mergedData, "video/mp4");
      break;
    case "WebM":
      tryNativePlayback(mergedData, "video/webm");
      break;
    case "MPEG-TS":
      tryNativePlayback(mergedData, "video/mp2t");
      break;
    default:
      tryAllPlaybackMethods(mergedData);
      break;
  }
}

// 尝试 FLV 播放
function tryFlvPlayback(data) {
  if (!flvjs.isSupported()) {
    console.log("浏览器不支持 flv.js，尝试其他方式");
    tryAllPlaybackMethods(data);
    return;
  }

  try {
    console.log("尝试 FLV 播放...");
    playbackMethod.value = "FLV.js";

    const blob = new Blob([data], { type: "video/x-flv" });
    const url = URL.createObjectURL(blob);

    player = flvjs.createPlayer({
      type: "flv",
      url: url,
      isLive: true,
      hasAudio: true,
      hasVideo: true,
      enableWorker: false,
      enableStashBuffer: false,
    });

    player.attachMediaElement(videoRef.value);

    player.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
      console.error("FLV 播放错误:", errorType, errorDetail, errorInfo);
      cleanupPlayer();
      tryAllPlaybackMethods(data);
    });

    player.on(flvjs.Events.MEDIA_INFO, (mediaInfo) => {
      console.log("FLV 媒体信息:", mediaInfo);
    });

    player.load();
    player
      .play()
      .then(() => {
        console.log("FLV 播放成功");
        status.value = "正在播放";
        isPlayerReady = true;
      })
      .catch((err) => {
        console.error("FLV 播放失败:", err);
        cleanupPlayer();
        tryAllPlaybackMethods(data);
      });
  } catch (error) {
    console.error("FLV 播放器创建失败:", error);
    tryAllPlaybackMethods(data);
  }
}

// 尝试原生播放
function tryNativePlayback(data, mimeType) {
  try {
    console.log(`尝试原生播放: ${mimeType}`);
    playbackMethod.value = `原生 HTML5 (${mimeType})`;

    const blob = new Blob([data], { type: mimeType });
    const url = URL.createObjectURL(blob);

    const video = videoRef.value;
    video.src = url;

    video.onloadedmetadata = () => {
      console.log(`✅ ${mimeType} 加载成功`);
      status.value = "正在播放";
      isPlayerReady = true;
    };

    video.onerror = (e) => {
      console.error(`${mimeType} 播放失败:`, e);
      if (mimeType !== "application/octet-stream") {
        tryAllPlaybackMethods(data);
      }
    };

    video.play().catch((err) => {
      console.error(`${mimeType} 播放启动失败:`, err);
    });
  } catch (error) {
    console.error(`${mimeType} 播放器创建失败:`, error);
    tryAllPlaybackMethods(data);
  }
}

// 尝试所有播放方式
function tryAllPlaybackMethods(data) {
  console.log("尝试所有播放方式...");

  const mimeTypes = [
    "video/mp4",
    "video/webm",
    "video/ogg",
    "video/x-msvideo",
    "video/quicktime",
    "video/x-flv",
    "application/octet-stream",
  ];

  let currentIndex = 0;

  function tryNext() {
    if (currentIndex >= mimeTypes.length) {
      console.error("所有播放方式都失败");
      status.value = "播放失败 - 不支持的格式";
      playbackMethod.value = "失败";
      return;
    }

    const mimeType = mimeTypes[currentIndex];
    currentIndex++;

    console.log(`尝试 MIME 类型: ${mimeType}`);

    const blob = new Blob([data], { type: mimeType });
    const url = URL.createObjectURL(blob);

    const video = videoRef.value;
    video.src = url;

    const timeout = setTimeout(() => {
      if (!isPlayerReady) {
        console.log(`${mimeType} 超时，尝试下一个`);
        tryNext();
      }
    }, 2000);

    video.onloadedmetadata = () => {
      clearTimeout(timeout);
      console.log(`✅ ${mimeType} 加载成功`);
      status.value = "正在播放";
      playbackMethod.value = `原生 HTML5 (${mimeType})`;
      isPlayerReady = true;
    };

    video.onerror = () => {
      clearTimeout(timeout);
      console.log(`${mimeType} 失败，尝试下一个`);
      tryNext();
    };
  }

  tryNext();
}

// 清理播放器
function cleanupPlayer() {
  if (player) {
    try {
      player.pause();
      player.unload();
      player.detachMediaElement();
      player.destroy();
    } catch (e) {
      console.error("清理 FLV 播放器失败:", e);
    }
    player = null;
  }

  if (mediaSource) {
    try {
      if (mediaSource.readyState === "open") {
        mediaSource.endOfStream();
      }
    } catch (e) {
      console.error("清理 MediaSource 失败:", e);
    }
    mediaSource = null;
  }

  if (videoRef.value) {
    videoRef.value.src = "";
    videoRef.value.load();
  }

  isPlayerReady = false;
}

// 连接 WebSocket
function connectWebSocket() {
  console.log("正在连接 WebSocket:", WS_URL);
  status.value = "正在连接...";

  ws = new WebSocket(WS_URL);
  ws.binaryType = "arraybuffer";

  ws.onopen = () => {
    console.log("WebSocket 连接成功");
    isConnected.value = true;
    status.value = "已连接";
  };

  ws.onmessage = (event) => {
    if (typeof event.data === "string") {
      console.log("收到文本消息:", event.data);
    } else {
      handleBinaryData(event.data);
    }
  };

  ws.onerror = (error) => {
    console.error("WebSocket 错误:", error);
    isConnected.value = false;
    status.value = "连接错误";
  };

  ws.onclose = (event) => {
    console.log("WebSocket 关闭:", event.code, event.reason);
    isConnected.value = false;
    isStreaming.value = false;
    status.value = "连接关闭";

    setTimeout(() => {
      if (!isConnected.value) {
        connectWebSocket();
      }
    }, 3000);
  };
}

// 重新连接
function reconnect() {
  if (ws) {
    ws.close();
  }
  setTimeout(connectWebSocket, 1000);
}

onMounted(() => {
  connectWebSocket();
});

onBeforeUnmount(() => {
  if (isStreaming.value) {
    stopStream();
  }
  cleanupPlayer();
  if (ws) {
    ws.close();
  }
});
</script>

<template>
  <div class="multi-format-player">
    <div class="header">
      <h2>多格式视频流播放器</h2>
      <div class="status">
        状态: {{ status }}
        <span :class="['indicator', { online: isConnected }]">●</span>
      </div>
    </div>

    <div class="info-panel">
      <div class="info-item"><strong>数据格式:</strong> {{ dataFormat }}</div>
      <div class="info-item"><strong>播放方式:</strong> {{ playbackMethod }}</div>
      <div class="info-item"><strong>缓冲块数:</strong> {{ dataBuffer.length }}</div>
    </div>

    <div class="controls">
      <button @click="startStream" :disabled="!isConnected || isStreaming" class="btn start">开始推流</button>

      <button @click="stopStream" :disabled="!isConnected || !isStreaming" class="btn stop">停止推流</button>

      <button @click="reconnect" :disabled="isConnected" class="btn reconnect">重新连接</button>
    </div>

    <div class="video-wrapper">
      <video ref="videoRef" controls muted autoplay class="video">您的浏览器不支持视频播放</video>
    </div>

    <div class="features">
      <h3>支持的格式</h3>
      <ul>
        <li>✅ FLV (使用 flv.js)</li>
        <li>✅ MP4 (原生 HTML5)</li>
        <li>✅ WebM (原生 HTML5)</li>
        <li>✅ MPEG-TS (原生 HTML5)</li>
        <li>✅ H.264 NAL 单元</li>
        <li>✅ 其他常见格式自动检测</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.multi-format-player {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.header h2 {
  margin: 0;
  color: #333;
}

.status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #666;
}

.indicator {
  font-size: 12px;
  color: #ff4d4f;
}

.indicator.online {
  color: #52c41a;
}

.info-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item {
  font-size: 14px;
  color: #555;
}

.controls {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn.start {
  background: #52c41a;
  color: white;
}

.btn.start:hover:not(:disabled) {
  background: #73d13d;
}

.btn.stop {
  background: #ff4d4f;
  color: white;
}

.btn.stop:hover:not(:disabled) {
  background: #ff7875;
}

.btn.reconnect {
  background: #1890ff;
  color: white;
}

.btn.reconnect:hover:not(:disabled) {
  background: #40a9ff;
}

.video-wrapper {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.video {
  width: 100%;
  height: 400px;
  background: #000;
}

.features {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.features h3 {
  margin-top: 0;
  color: #333;
}

.features ul {
  margin: 0;
  padding-left: 20px;
}

.features li {
  margin-bottom: 8px;
  color: #555;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .info-panel {
    grid-template-columns: 1fr;
  }

  .video {
    height: 250px;
  }
}
</style>
