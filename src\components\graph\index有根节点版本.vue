<template>
  <div class="wrap" :class="{ 'drag-mode': isDragging }" @click="fabOpen = false">
    <div ref="containerEl" class="graph"></div>

    <!-- 悬浮圆球（FAB） -->
    <div class="fab" :class="{ open: fabOpen }">
      <el-button class="fab-btn" circle @click.stop="fabOpen = !fabOpen">
        <el-icon><Operation /></el-icon>
      </el-button>

      <!-- 从左侧弹出 -->
      <transition name="fab-left-pop">
        <div v-show="fabOpen" class="fab-menu-wrap">
          <ul class="fab-menu">
            <li class="fab-item" @click="handleToCenter()"><span class="dot"></span> 回到中心点</li>
            <li class="fab-item" @click="handleFab('tree')"><span class="dot"></span> 垂直布局</li>
            <li class="fab-item" @click="handleFab('dendrogram')"><span class="dot"></span> 生态树布局</li>
            <li class="fab-item" @click="handleFab('radial')"><span class="dot"></span> 生态图布局</li>
          </ul>
        </div>
      </transition>
    </div>

    <!-- 右键信息面板（可选） -->
    <div v-if="panel.visible" class="info-panel" @contextmenu.prevent>
      <el-card>
        <div class="panel-hd">
          <div class="title">{{ panel.title }}</div>
          <div class="sub">{{ panel.sub }}</div>
          <el-button class="close" @click="panel.visible = false">×</el-button>
        </div>
        <div class="panel-bd">
          <div class="kv" v-for="(r, i) in panel.attrs" :key="i">
            <div class="k">{{ r.name ?? r.code }}</div>
            <div class="v">{{ r.value }}</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, reactive, watch, nextTick } from "vue";
import { Graph, treeToGraphData } from "@antv/g6";
import { Operation } from "@element-plus/icons-vue";

/* ========== Props ========== */
const props = defineProps({ data: { type: Object, default: () => ({}) } });

/* ========== 状态 ========== */
const graphRef = ref(null);
const isDragging = ref(false);
const containerEl = ref(null);
const fabOpen = ref(false);
const lastSig = ref(null);
const colorAuto = ref({});
const panel = reactive({ visible: false, title: "", sub: "", attrs: [] });
const currentLayoutKey = ref("dendrogram"); // 默认用 LR 树

/* ========== 布局映射（仅用于“新建图”时） ========== */
const LAYOUT_PRESETS = {
  tree: {
    layout: { type: "dendrogram", direction: "TB", nodeSep: 150, rankSep: 200 },
    edgeType: "cubic-vertical",
  },
  dendrogram: {
    layout: { type: "dendrogram", direction: "LR", nodeSep: 130, rankSep: 200 },
    edgeType: "cubic-horizontal",
  },
  radial: {
    layout: { type: "dendrogram", radial: true, nodeSep: 130, rankSep: 200 },
    edgeType: "line",
  },
};

/* ========== 配色 ========== */
const COLOR_BY_TYPE = {
  unit: { fill: "#FFC857", stroke: "#B8860B" },
  person: { fill: "#4F8DF7", stroke: "#1F5CC4" },
  train: { fill: "#33C08D", stroke: "#1E8A62" },
  other: { fill: "#a9a9a9", stroke: "#6e6e6e" },
};
const PALETTE = [
  { fill: "#FFC857", stroke: "#B8860B" },
  { fill: "#4F8DF7", stroke: "#1F5CC4" },
  { fill: "#33C08D", stroke: "#1E8A62" },
  { fill: "#A47CF3", stroke: "#6A4FB6" },
  { fill: "#FF7A90", stroke: "#C94A5A" },
  { fill: "#8BD3E6", stroke: "#4E98A8" },
];
function computeColorAuto(gData) {
  const types = Array.from(new Set((gData.nodes || []).map((n) => n.data?.type || "other")));
  const map = {};
  let i = 0;
  for (const t of types) map[t] = COLOR_BY_TYPE[t] || PALETTE[i++ % PALETTE.length];
  return map;
}

/* ========== 数据→树→gData ========== */
function buildForestFromLinks(raw, options = {}) {
  const { reverseByRelation = new Set(), maxDepth = 16 } = options;
  const nodes = raw?.nodes || [];
  const links = raw?.links || [];
  const nodeById = new Map(nodes.map((n) => [String(n.id), n]));

  const getTypeKey = (n) => n?.labels?.[0] || n?.label || "other";
  const getAttr = (n, code) => n?.attrs?.find((a) => a.code === code || a.name === code)?.value ?? "";
  const getName = (n) => n?.nodeName || getAttr(n, "name") || n?.entityId || n?.id || "";
  const makeId = (type, id, ctx) => (ctx ? `${type}:${id}@${ctx}` : `${type}:${id}`);
  const toTreeNode = (n, type, ctx, parentRelation) => ({
    id: makeId(type, String(n.id), ctx),
    name: getName(n),
    collapsed: false,
    data: {
      originId: String(n.id),
      entityId: n.entityId,
      labels: n.labels,
      type,
      attrs: n.attrs,
      parentRelation: parentRelation || "",
    },
  });

  const out = new Map();
  const indeg = new Map();
  for (const e of links) {
    const rel = e.name || "";
    let s = String(e.source),
      t = String(e.target);
    if (reverseByRelation.has(rel)) [s, t] = [t, s];
    if (!out.has(s)) out.set(s, []);
    out.get(s).push({ to: t, relation: rel });
    indeg.set(t, (indeg.get(t) || 0) + 1);
  }

  const allIds = nodes.map((n) => String(n.id));
  const rootIds = allIds.filter((id) => !indeg.get(id));

  function dfs(curId, ctx, depth, stack) {
    const n = nodeById.get(curId);
    if (!n) return null;
    const node = toTreeNode(n, getTypeKey(n), ctx, stack?.parentRelation);
    if (depth >= maxDepth) return node;
    const seen = stack?.set || new Set();
    if (seen.has(curId)) return node;
    const nextSeen = new Set(seen);
    nextSeen.add(curId);
    const children = [];
    for (const e of out.get(curId) || []) {
      const child = dfs(e.to, `p:${curId}`, depth + 1, { set: nextSeen, parentRelation: e.relation });
      if (child) children.push(child);
    }
    if (children.length) node.children = children;
    return node;
  }

  if (rootIds.length) {
    const forest = [];
    for (const rid of rootIds) forest.push(dfs(rid, "", 0, { set: new Set(), parentRelation: "" }));
    return forest.filter(Boolean);
  }

  const virtualChildren = [];
  for (const id of allIds) {
    const sub = dfs(id, "root", 0, { set: new Set(), parentRelation: "" });
    if (sub) virtualChildren.push(sub);
  }
  return [{ id: "ROOT", name: "ROOT", data: { type: "other" }, children: virtualChildren }];
}

/**
 * 从树结构转换为图数据
 * @param {Object} treeRoot - 树的根节点
 * @returns {Object} - 图数据对象
 */
function makeGraphData(raw) {
  const forests = buildForestFromLinks(raw);
  const treeRoot = forests.length === 1 ? forests[0] : { id: "ROOT", name: "ROOT", children: forests };
  const gData = treeToGraphData(treeRoot);

  const nodeIndex = new Map((gData.nodes || []).map((n) => [String(n.id), n]));
  gData.edges = (gData.edges || []).map((e, idx) => {
    const relation = nodeIndex.get(String(e.target))?.data?.parentRelation || "";
    const id = e.id || `e-${idx}-${e.source}-${e.target}`;
    return { ...e, id, data: { ...(e.data || {}), relation } };
  });
  return gData;
}

/* 容器可见/有尺寸再渲染，避免 0 宽 */
async function waitContainerReady(maxTries = 30) {
  let tries = 0;
  while (tries++ < maxTries) {
    await new Promise((r) => requestAnimationFrame(r));
    const el = containerEl.value;
    if (!el) continue;
    const { width, height } = el.getBoundingClientRect();
    const cs = window.getComputedStyle(el);
    const ok = width > 20 && height > 20 && cs.display !== "none" && cs.visibility !== "hidden";
    if (ok) return true;
  }
  return false;
}

/* 观察容器尺寸变化，自适应画布 */
let ro = null;
function attachResizeObserver(g) {
  detachResizeObserver();
  if (!containerEl.value || !g) return;
  ro = new ResizeObserver(() => {
    const el = containerEl.value;
    if (!el) return;
    const { width, height } = el.getBoundingClientRect();
    if (width && height) {
      g.resize(width, height);
      g.fitView(24);
    }
  });
  ro.observe(containerEl.value);
}
function detachResizeObserver() {
  try {
    ro?.disconnect?.();
  } catch {}
  ro = null;
}

/* ========== 工具：清空状态 ========== */
function clearAllStates(g) {
  const map = {};
  g.getNodeData().forEach((n) => (map[n.id] = []));
  g.getEdgeData().forEach((e) => (map[e.id] = []));
  g.setElementState(map);
}

// 点击菜单项后调用你原本的 switchLayout
function handleFab(type) {
  try {
    // 这里假设你在本组件里已有 switchLayout(layoutType) 方法
    // 若在别处，请改成合适的调用方式或用 emit 派发事件
    switchLayout(type);
  } finally {
    fabOpen.value = false;
  }
}

const handleToCenter = () => {
  graphRef.value?.fitView(24);
};

/* ========== 样式 ========== */
const EDGE_STYLE = {
  endArrow: true,
  labelText: (d) => d?.data?.relation ?? "",
  labelPlacement: "center",
  labelAutoRotate: false,
  labelBackground: true,
  labelPadding: [2, 6, 2, 6],
};
function isLeafNode(d) {
  return !d?.children || d.children.length === 0;
}

/* ========== 创建一张新图（按布局 key） ========== */
async function createGraph(layoutKey) {
  const preset = LAYOUT_PRESETS[layoutKey] || LAYOUT_PRESETS.dendrogram;
  const gData = makeGraphData(props.data || {});
  colorAuto.value = computeColorAuto(gData);

  // 等容器有真实尺寸
  const ok = await waitContainerReady();
  if (!ok) return null;
  const el = containerEl.value;
  el.innerHTML = "";
  const { width, height } = el.getBoundingClientRect();

  const g = new Graph({
    container: el,
    devicePixelRatio: window.devicePixelRatio,
    autoFit: false,
    width,
    height,
    data: gData,
    node: {
      type: "rect",
      style: (d) => {
        const t = d?.data?.type ?? "other";
        const { fill, stroke } = colorAuto.value[t] || COLOR_BY_TYPE.other;
        return {
          fill,
          stroke,
          lineWidth: 1.5,
          radius: 20,
          padding: [4, 8, 4, 8],
          labelText: `${d.name ?? d.id}`,
          labelFill: "#fff",
          labelPlacement: isLeafNode(d) ? "right" : "left",
          labelBackground: true,
        };
      },
      animation: { enter: false, update: false },
    },
    edge: {
      type: preset.edgeType,
      style: EDGE_STYLE,
      animation: { enter: false, update: false },
    },
    layout: preset.layout, // ★ 只在“新建时”指定布局，后续不再 setLayout
    behaviors: [
      { type: "drag-canvas", key: "drag-canvas", enable: (evt) => !isDragging.value && evt?.targetType === "canvas" },
      { type: "zoom-canvas", key: "zoom-canvas", enable: (evt) => evt?.targetType === "canvas" },
      { type: "collapse-expand", trigger: "click", shouldBegin: (evt) => evt?.targetType === "node" },
      {
        type: "drag-element",
        key: "drag-node",
        enable: (evt) => evt?.targetType === "node",
        dropEffect: "move",
        hideEdge: "all",
        shadow: false,
        cursor: { grab: "grab", grabbing: "grabbing" },
      },
    ],
    plugins: [
      { type: "contextmenu", getItems: () => [] },
      // { type: "minimap", size: [240, 160] },
      { type: "fullscreen", key: "fullscreen" },
    ],
    theme: "dark",
  });

  // 交互事件
  g.on("node:dragstart", () => (isDragging.value = true));
  g.on("node:dragend", () => (isDragging.value = false));
  g.on("canvas:mouseup", () => (isDragging.value = false));

  g.on("node:contextmenu", (evt) => {
    evt.originalEvent?.preventDefault?.();
    const id = evt?.target?.id;
    if (!id) return;
    const nd = g.getNodeData(id);
    panel.title = nd?.name ?? id;
    panel.sub = nd?.data?.type ?? nd?.data?.labels?.[0] ?? "";
    panel.attrs = nd?.data?.attrs ?? [];
    panel.visible = true;
  });
  g.on("canvas:click", () => {
    panel.visible = false;
    clearAllStates(g);
  });

  await g.render();
  g.fitView(24);
  attachResizeObserver(g);
  return g;
}

/* ========== 销毁并重建（用于切换布局） ========== */
async function rebuildGraph(layoutKey) {
  // 销毁旧实例
  if (graphRef.value) {
    try {
      graphRef.value.destroy();
    } catch (e) {
      // ignore
    }
    graphRef.value = null;
    // Vue/浏览器下一帧再建，避免同容器竞态
    await nextTick();
  }
  // 新建
  graphRef.value = await createGraph(layoutKey);
}

/* ========== 切换布局（完全重建） ========== */
const switchLayout = async (key) => {
  currentLayoutKey.value = key;
  await rebuildGraph(key);
};

/* ========== 数据签名，减少无谓刷新 ========== */
function makeDataSignature(d) {
  const nodes = d?.nodes ?? [];
  const links = d?.links ?? [];
  let h = 0x811c9dc5;
  const mix = (s) => {
    for (let i = 0; i < s.length; i++) {
      h ^= s.charCodeAt(i);
      h = (h >>> 0) * 16777619;
    }
  };
  mix(String(nodes.length));
  for (const n of nodes) mix(`${n.id}|${n.updatedAt ?? n.version ?? ""}`);
  mix(String(links.length));
  for (const e of links) mix(`${e.source}->${e.target}|${e.name ?? ""}`);
  return h >>> 0;
}

/* ========== props.data 变化：仅更新数据并重新渲染，不 setLayout ========== */
async function updateGraphFromProps(raw) {
  const g = graphRef.value;
  if (!g) return;
  const gData = makeGraphData(raw);
  colorAuto.value = computeColorAuto(gData);

  g.setData(gData);
  // 重新声明节点样式（颜色映射可能因类型新增而变化）
  g.setNode({
    type: "rect",
    style: (d) => {
      const t = d?.data?.type ?? "other";
      const { fill, stroke } = colorAuto.value[t] || COLOR_BY_TYPE.other;
      return {
        fill,
        stroke,
        lineWidth: 1.5,
        radius: 20,
        padding: [4, 8, 4, 8],
        labelText: `${d.name ?? d.id}`,
        labelFill: "#fff",
        labelPlacement: isLeafNode(d) ? "right" : "left",
        labelBackground: true,
      };
    },
    animation: { enter: false, update: false },
  });

  await g.render(); // 让当前实例按“当初创建时的布局配置”跑一次布局
  g.fitView(24);
}

/* ========== 生命周期 ========== */
onMounted(async () => {
  graphRef.value = await createGraph(currentLayoutKey.value);
  lastSig.value = makeDataSignature(props.data || {});
});

onBeforeUnmount(() => {
  detachResizeObserver();
  graphRef.value?.destroy();
  graphRef.value = null;
});

/* ========== 监听外部数据变化 ========== */
watch(
  () => props.data,
  async (raw) => {
    const sig = makeDataSignature(raw || {});
    if (sig === lastSig.value) return;
    lastSig.value = sig;
    await updateGraphFromProps(raw || {});
  },
  { deep: true, flush: "post" }
);
</script>

<style lang="scss" scoped>
.wrap {
  position: relative;
  width: 100%;
  height: 100%;
}
.toolbar {
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}
.graph {
  width: 100%;
  height: 100%;
  cursor: grab;
}
.graph:active {
  cursor: grabbing;
}

/* 拖拽时可淡出面板/工具栏，避免干扰（可选） */
.drag-mode .toolbar,
.drag-mode .info-panel {
  opacity: 0.4;
  pointer-events: none;
}

/* ===== 悬浮球本体 ===== */
.fab {
  position: fixed;
  right: 24px;
  bottom: 50px;
  z-index: 1000;
}

/* ===== 左侧弹出容器（不改变悬浮球位置）===== */
.fab-menu-wrap {
  position: absolute;
  /* 菜单整体放在按钮左侧，留 12px 间距 */
  right: calc(40px + 12px);
  top: 50%;
  transform: translateY(-50%);
  transform-origin: center right; /* 动画基点在右侧 */
  z-index: -1; /* 保证不盖住球本体的 hover/active 效果 */
}

/* 菜单卡片 */
.fab-menu {
  list-style: none;
  margin: 0;
  padding: 8px 8px;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.18), 0 2px 10px rgba(0, 0, 0, 0.08);
  min-width: 120px;
  user-select: none;
  position: relative;
}

/* 指向球的小三角 */
.fab-menu::before {
  content: "";
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-left: 6px solid #fff;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.06));
}

/* 菜单项 */
.fab-item {
  font-size: 14px;
  line-height: 20px;
  padding: 10px 12px;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.15s ease, transform 0.05s ease;
  white-space: nowrap;
}
.fab-item:hover {
  background: #f5f7fa;
}
.fab-item:active {
  transform: scale(0.99);
}
.fab-item .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #409eff;
}

/* 左向弹出过渡（不影响球的定位） */
.fab-left-pop-enter-from,
.fab-left-pop-leave-to {
  opacity: 0;
  transform: translate(-6px, -50%) scale(0.98); /* 从右→左轻推入 */
}
.fab-left-pop-enter-active,
.fab-left-pop-leave-active {
  transition: all 0.16s ease;
}

/* 信息面板 */
.info-panel {
  position: absolute;
  right: 12px;
  top: 64px;
  width: 340px;
  max-height: calc(100% - 96px);
  background: #111;
  color: #fff;
  border: 1px solid #333;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.35);
  z-index: 12;
}
.panel-hd {
  display: flex;
  align-items: start;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid #222;
}
.title {
  font-weight: 700;
  font-size: 16px;
  flex: 1;
}
.sub {
  color: #aaa;
  font-size: 12px;
  margin-top: 2px;
}
.close {
  background: transparent;
  border: none;
  color: #aaa;
  font-size: 20px;
  cursor: pointer;
}
.panel-bd {
  padding: 10px 12px;
  max-height: 520px;
  overflow: auto;
}
.kv {
  display: grid;
  grid-template-columns: 110px 1fr;
  gap: 6px 10px;
  padding: 6px 0;
  border-bottom: 1px dashed #222;
}
.k {
  color: #409eff;
  word-break: break-all;
}
.v {
  color: #ddd; /* 深色面板上可读 */
  word-break: break-all;
}

/* Tooltip */
.g6-tip {
  max-width: 320px;
  background: rgba(0, 0, 0, 0.9);
  color: #fff;
  border: 1px solid #333;
  padding: 8px 10px;
  border-radius: 6px;
}
.g6-tip .hd {
  font-weight: 700;
  margin-bottom: 6px;
}
.g6-tip .row {
  font-size: 12px;
  margin: 2px 0;
}
</style>
