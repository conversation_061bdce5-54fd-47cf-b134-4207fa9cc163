<template>
  <div class="graph-layout-container">
    <!-- 查询模块 -->
    <div class="query-panel" :class="{ collapsed: isCollapsed }">
      <el-card class="query-card">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" class="query-tabs">
          <el-tab-pane label="实体查询" name="entity"></el-tab-pane>
          <el-tab-pane label="路径查询" name="path"></el-tab-pane>
          <el-tab-pane label="多跳查询" name="multi"></el-tab-pane>
        </el-tabs>

        <!-- 查询表单 -->
        <div class="query-form" v-show="!isCollapsed">
          <!-- 实体查询表单 -->
          <div v-if="activeTab === 'entity'">
            <!-- 开始标签 -->
            <div class="form-item">
              <div class="form-label">
                <el-icon><PriceTag /></el-icon>
                开始标签:
              </div>
              <el-select
                v-model="queryForm.startTag"
                placeholder="iq数据"
                clearable
                class="form-select"
                filterable
                remote
                :remote-method="handleTagSearch"
                :loading="tagLoading"
              >
                <el-option v-for="item in tagOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>

            <!-- 开始实体 -->
            <div class="form-item">
              <div class="form-label">
                <el-icon><User /></el-icon>
                开始实体:
              </div>
              <el-select
                v-model="queryForm.startEntity"
                placeholder="请选择实体"
                clearable
                class="form-select"
                filterable
                remote
                :remote-method="handleEntitySearch"
                :loading="entityLoading"
              >
                <el-option v-for="item in entityOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </div>

          <!-- 路径查询表单 -->
          <div v-if="activeTab === 'path'">
            <!-- 最大路径 -->
            <div class="form-item">
              <div class="form-label">
                <el-icon><InfoFilled /></el-icon>
                最大路径:
              </div>
              <div class="slider-container">
                <el-slider v-model="queryForm.maxPath" :min="1" :max="10" show-tooltip />
              </div>
            </div>

            <!-- 起始标签 -->
            <div class="form-item">
              <div class="form-label">
                <el-icon><PriceTag /></el-icon>
                起始标签:
              </div>
              <el-select v-model="queryForm.pathStartTag" placeholder="iq数据" clearable class="form-select">
                <el-option v-for="item in tagOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>

            <!-- 起始实体 -->
            <div class="form-item">
              <div class="form-label">
                <el-icon><User /></el-icon>
                起始实体:
              </div>
              <el-select v-model="queryForm.pathStartEntity" placeholder="请选择起始实体" clearable class="form-select">
                <el-option v-for="item in entityOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>

            <!-- 终点标签 -->
            <div class="form-item">
              <div class="form-label">
                <el-icon><PriceTag /></el-icon>
                终点标签:
              </div>
              <el-select v-model="queryForm.pathEndTag" placeholder="iq数据" clearable class="form-select">
                <el-option v-for="item in tagOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>

            <!-- 终点实体 -->
            <div class="form-item">
              <div class="form-label">
                <el-icon><User /></el-icon>
                终点实体:
              </div>
              <el-select v-model="queryForm.pathEndEntity" placeholder="请选择终点实体" clearable class="form-select">
                <el-option v-for="item in entityOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </div>

          <!-- 多跳查询表单 -->
          <div v-if="activeTab === 'multi'">
            <!-- 多跳参数 -->
            <div class="form-item">
              <div class="form-label">
                <el-icon><InfoFilled /></el-icon>
                多跳参数:
              </div>
              <div class="slider-container">
                <el-slider v-model="queryForm.multiHops" :min="1" :max="6" show-tooltip />
              </div>
            </div>

            <!-- 标签 -->
            <div class="form-item">
              <div class="form-label">
                <el-icon><PriceTag /></el-icon>
                标签:
              </div>
              <el-select v-model="queryForm.multiTag" placeholder="iq数据" clearable class="form-select">
                <el-option v-for="item in tagOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>

            <!-- 实体 -->
            <div class="form-item">
              <div class="form-label">
                <el-icon><User /></el-icon>
                实体:
              </div>
              <el-select v-model="queryForm.multiEntity" placeholder="请选择实体" clearable class="form-select">
                <el-option v-for="item in entityOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </div>

          <!-- 查询按钮 -->
          <div class="form-actions">
            <el-button size="small" @click="handleQuery">查 询</el-button>
          </div>
        </div>

        <!-- 收起/展开按钮 -->
        <div class="collapse-btn" @click="toggleCollapse">
          <el-icon>
            <ArrowUp v-if="!isCollapsed" />
            <ArrowDown v-if="isCollapsed" />
          </el-icon>
          {{ isCollapsed ? "展开" : "收起" }}
        </div>
      </el-card>
    </div>

    <!-- 图形组件 -->
    <GraphG6
      ref="graphRef"
      v-model:panelVisible="panelVisible"
      :data="data"
      :attrs="panelAttrs"
      @update:panel="handleUpdatePanel"
      @load-children="onLoadChildren"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { InfoFilled, PriceTag, User, ArrowUp, ArrowDown } from "@element-plus/icons-vue";
import GraphG6 from "@/components/graph/index.vue";
import { fetchGraphData, fetchAttrsData, fetchChildren } from "@/api";

// 查询面板状态
const activeTab = ref("entity");
const panelVisible = ref(false); // 右键信息面板是否显示
const panelAttrs = ref([]); // 右键信息面板属性
const graphRef = ref(null);
const isCollapsed = ref(false);
const tagOptions = ref([
  { label: "iq数据", value: "iq" },
  { label: "军事人员表", value: "personnel" },
  { label: "军事训练表", value: "training" },
  { label: "军事作战单位表", value: "unit" },
]);

const entityOptions = ref([
  { label: "张伟", value: "zhangwei" },
  { label: "方振华", value: "fangzhenhua" },
  { label: "孙志勇", value: "sunzhiyong" },
  { label: "第1装甲师", value: "unit1" },
]);

// 查询表单数据
const queryForm = reactive({
  // 实体查询
  startTag: "",
  startEntity: "",

  // 路径查询
  maxPath: 3,
  pathStartTag: "",
  pathStartEntity: "",
  pathEndTag: "",
  pathEndEntity: "",

  // 多跳查询
  multiHops: 3,
  multiTag: "",
  multiEntity: "",
});

// 切换收起/展开
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 处理查询
const handleQuery = () => {
  console.log("查询参数:", {
    tab: activeTab.value,
    ...queryForm,
  });
  ElMessage.success("查询功能待实现");
};

// 搜索状态变量
const tagLoading = ref(false);
const entityLoading = ref(false);

// 标签远程搜索方法
const handleTagSearch = (query) => {
  if (!query) {
    // 如果查询为空，恢复默认选项
    tagOptions.value = [
      { label: "iq数据", value: "iq" },
      { label: "军事人员表", value: "personnel" },
      { label: "军事训练表", value: "training" },
      { label: "军事作战单位表", value: "unit" },
    ];
    return;
  }

  // 模拟远程搜索加载状态
  tagLoading.value = true;

  // 实际项目中这里应该调用接口查询
  setTimeout(() => {
    const filtered = [
      { label: "iq数据", value: "iq" },
      { label: "军事人员表", value: "personnel" },
      { label: "军事训练表", value: "training" },
      { label: "军事作战单位表", value: "unit" },
    ].filter(
      (item) =>
        item.label.toLowerCase().includes(query.toLowerCase()) || item.value.toLowerCase().includes(query.toLowerCase())
    );

    tagOptions.value = filtered;
    tagLoading.value = false;
  }, 300);
};

// 实体远程搜索方法
const handleEntitySearch = (query) => {
  if (!query) {
    // 恢复默认选项
    entityOptions.value = [
      { label: "张伟", value: "zhangwei" },
      { label: "方振华", value: "fangzhenhua" },
      { label: "孙志勇", value: "sunzhiyong" },
      { label: "第1装甲师", value: "unit1" },
    ];
    return;
  }

  entityLoading.value = true;

  // 模拟接口请求
  setTimeout(() => {
    const filtered = [
      { label: "张伟", value: "zhangwei" },
      { label: "方振华", value: "fangzhenhua" },
      { label: "孙志勇", value: "sunzhiyong" },
      { label: "第1装甲师", value: "unit1" },
    ].filter(
      (item) =>
        item.label.toLowerCase().includes(query.toLowerCase()) || item.value.toLowerCase().includes(query.toLowerCase())
    );

    entityOptions.value = filtered;
    entityLoading.value = false;
  }, 300);
};

const data = ref();
const getData = async () => {
  const res = await fetchGraphData();
  data.value = res.data;
};

/**
 * 处理右键信息面板更新
 * @param {Object} panel - 右键信息面板数据
 */
const handleUpdatePanel = (panel) => {
  fetchAttrsData().then((res) => {
    panelAttrs.value = res.data;
  });
  panelVisible.value = true;
};

/**
 * 加载子节点
 * @param {Object} params - 加载参数
 * @param {string} params.id - 节点ID
 * @param {Object} params.raw - 原始节点数据
 */
async function onLoadChildren({ id }) {
  const res = await fetchChildren(id); // 你的接口
  const { nodes, links } = res.data;
  if (!nodes?.length && !links?.length) {
    // 已经是最后节点
    ElMessage.info("已经是最后节点");
    return;
  }
  graphRef.value?.applyChildren(id, { nodes, links }); // ★ 走增量
}

onMounted(() => {
  getData();
});
</script>

<style scoped>
.graph-layout-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.query-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 320px;
  z-index: 1000;
  transition: all 0.3s ease;
}

.query-panel.collapsed {
  width: 320px;
}

.query-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.query-tabs {
  margin-bottom: 0;
}

.query-tabs :deep(.el-tabs__header) {
  margin: 0;
  border-bottom: 1px solid #e4e7ed;
}

.query-tabs :deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.query-tabs :deep(.el-tabs__item) {
  padding: 0 16px;
  font-size: 14px;
  color: #606266;
}

.query-tabs :deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 500;
}

.query-form {
  padding: 16px 0 0 0;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.form-label .el-icon {
  margin-right: 6px;
  color: #409eff;
}

.form-select {
  width: 100%;
}

.slider-container {
  padding: 0 12px;
}

.form-actions {
  margin-top: 20px;
  padding-top: 16px;
  text-align: right;
}

.collapse-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  cursor: pointer;
  color: #909399;
  font-size: 12px;
  transition: color 0.3s ease;
}

.collapse-btn:hover {
  color: #409eff;
}

.collapse-btn .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .query-panel {
    width: calc(100% - 40px);
    max-width: 320px;
  }
}
</style>
