<template>
  <div class="drag-resize-demo">
    <h1>拖拽 + 调整大小组合演示</h1>
    
    <div class="demo-container">
      <div class="demo-section">
        <h2>基础拖拽 + 调整大小</h2>
        <p>可以拖拽移动，也可以调整大小</p>
        <div 
          class="draggable-resizable basic"
          v-resize="{
            minWidth: 150,
            minHeight: 100,
            handles: ['se', 'e', 's'],
            draggable: true,
            onResize: onBasicResize,
            onDrag: onBasicDrag
          }"
        >
          <div class="content">
            <h3>可拖拽调整的容器</h3>
            <p>位置: {{ basicState.x }}px, {{ basicState.y }}px</p>
            <p>尺寸: {{ basicState.width }}px × {{ basicState.height }}px</p>
            <p>拖拽我移动位置，拖拽边缘调整大小</p>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h2>指定拖拽手柄</h2>
        <p>只有标题栏可以拖拽，其他区域用于调整大小</p>
        <div 
          class="draggable-resizable with-header"
          v-resize="{
            minWidth: 200,
            minHeight: 150,
            handles: ['n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw'],
            draggable: true,
            dragHandle: '.drag-header',
            onResize: onHeaderResize,
            onDrag: onHeaderDrag
          }"
        >
          <div class="drag-header">
            <h3>📋 窗口标题 - 拖拽我移动</h3>
          </div>
          <div class="content">
            <p>位置: {{ headerState.x }}px, {{ headerState.y }}px</p>
            <p>尺寸: {{ headerState.width }}px × {{ headerState.height }}px</p>
            <p>只能通过标题栏拖拽移动</p>
            <p>可以从任意边缘调整大小</p>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h2>多个独立窗口</h2>
        <p>多个可拖拽调整的窗口</p>
        <div 
          v-for="(window, index) in windows" 
          :key="index"
          class="draggable-resizable window"
          :style="{ 
            left: window.x + 'px', 
            top: window.y + 'px',
            width: window.width + 'px',
            height: window.height + 'px',
            zIndex: window.zIndex
          }"
          v-resize="{
            minWidth: 180,
            minHeight: 120,
            handles: ['se', 'e', 's'],
            draggable: true,
            dragHandle: '.window-header',
            onResize: (data) => onWindowResize(index, data),
            onDrag: (data) => onWindowDrag(index, data),
            onDragStart: () => bringToFront(index)
          }"
          @mousedown="bringToFront(index)"
        >
          <div class="window-header">
            <span>窗口 {{ index + 1 }}</span>
            <button @click="closeWindow(index)" class="close-btn">×</button>
          </div>
          <div class="window-content">
            <p>位置: {{ window.x }}, {{ window.y }}</p>
            <p>尺寸: {{ window.width }} × {{ window.height }}</p>
            <p>层级: {{ window.zIndex }}</p>
          </div>
        </div>
        
        <div class="controls">
          <button @click="addWindow" class="btn">添加新窗口</button>
          <button @click="resetWindows" class="btn">重置所有窗口</button>
        </div>
      </div>

      <div class="demo-section">
        <h2>API 控制演示</h2>
        <div class="controls">
          <button @click="moveToCenter" class="btn">移动到中心</button>
          <button @click="resetSizeAndPosition" class="btn">重置大小和位置</button>
          <button @click="toggleDrag" class="btn">
            {{ apiDemoState.dragEnabled ? '禁用' : '启用' }}拖拽
          </button>
          <button @click="getInfo" class="btn">获取信息</button>
        </div>
        <div 
          ref="apiDemoRef"
          class="draggable-resizable api-demo"
          v-resize="{
            minWidth: 200,
            minHeight: 150,
            handles: ['e', 's', 'se'],
            draggable: true,
            onResize: onApiResize,
            onDrag: onApiDrag
          }"
        >
          <div class="content">
            <h3>API 控制演示</h3>
            <p>位置: {{ apiDemoState.x }}px, {{ apiDemoState.y }}px</p>
            <p>尺寸: {{ apiDemoState.width }}px × {{ apiDemoState.height }}px</p>
            <p>拖拽: {{ apiDemoState.dragEnabled ? '启用' : '禁用' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 基础演示状态
const basicState = reactive({
  x: 50,
  y: 50,
  width: 300,
  height: 200
})

const onBasicResize = (data) => {
  basicState.width = Math.round(data.width)
  basicState.height = Math.round(data.height)
}

const onBasicDrag = (data) => {
  basicState.x = Math.round(data.x)
  basicState.y = Math.round(data.y)
}

// 带标题栏的演示状态
const headerState = reactive({
  x: 350,
  y: 50,
  width: 350,
  height: 250
})

const onHeaderResize = (data) => {
  headerState.width = Math.round(data.width)
  headerState.height = Math.round(data.height)
}

const onHeaderDrag = (data) => {
  headerState.x = Math.round(data.x)
  headerState.y = Math.round(data.y)
}

// 多窗口演示
const windows = ref([
  { x: 50, y: 350, width: 200, height: 150, zIndex: 1 },
  { x: 280, y: 380, width: 200, height: 150, zIndex: 2 },
  { x: 510, y: 410, width: 200, height: 150, zIndex: 3 }
])

let maxZIndex = 3

const onWindowResize = (index, data) => {
  windows.value[index].width = Math.round(data.width)
  windows.value[index].height = Math.round(data.height)
}

const onWindowDrag = (index, data) => {
  windows.value[index].x = Math.round(data.x)
  windows.value[index].y = Math.round(data.y)
}

const bringToFront = (index) => {
  maxZIndex++
  windows.value[index].zIndex = maxZIndex
}

const addWindow = () => {
  maxZIndex++
  windows.value.push({
    x: 100 + windows.value.length * 30,
    y: 400 + windows.value.length * 30,
    width: 200,
    height: 150,
    zIndex: maxZIndex
  })
}

const closeWindow = (index) => {
  windows.value.splice(index, 1)
}

const resetWindows = () => {
  windows.value = [
    { x: 50, y: 350, width: 200, height: 150, zIndex: 1 },
    { x: 280, y: 380, width: 200, height: 150, zIndex: 2 },
    { x: 510, y: 410, width: 200, height: 150, zIndex: 3 }
  ]
  maxZIndex = 3
}

// API 演示
const apiDemoRef = ref(null)
const apiDemoState = reactive({
  x: 50,
  y: 600,
  width: 300,
  height: 200,
  dragEnabled: true
})

const onApiResize = (data) => {
  apiDemoState.width = Math.round(data.width)
  apiDemoState.height = Math.round(data.height)
}

const onApiDrag = (data) => {
  apiDemoState.x = Math.round(data.x)
  apiDemoState.y = Math.round(data.y)
}

const moveToCenter = () => {
  if (apiDemoRef.value && apiDemoRef.value._resizeAPI) {
    const centerX = (window.innerWidth - apiDemoState.width) / 2
    const centerY = (window.innerHeight - apiDemoState.height) / 2
    apiDemoRef.value._resizeAPI.setPosition(centerX, centerY)
    apiDemoState.x = centerX
    apiDemoState.y = centerY
  }
}

const resetSizeAndPosition = () => {
  if (apiDemoRef.value && apiDemoRef.value._resizeAPI) {
    apiDemoRef.value._resizeAPI.setSize(300, 200)
    apiDemoRef.value._resizeAPI.setPosition(50, 600)
    apiDemoState.width = 300
    apiDemoState.height = 200
    apiDemoState.x = 50
    apiDemoState.y = 600
  }
}

const toggleDrag = () => {
  if (apiDemoRef.value && apiDemoRef.value._resizeAPI) {
    if (apiDemoState.dragEnabled) {
      apiDemoRef.value._resizeAPI.disableDrag()
    } else {
      apiDemoRef.value._resizeAPI.enableDrag()
    }
    apiDemoState.dragEnabled = !apiDemoState.dragEnabled
  }
}

const getInfo = () => {
  if (apiDemoRef.value && apiDemoRef.value._resizeAPI) {
    const size = apiDemoRef.value._resizeAPI.getSize()
    const position = apiDemoRef.value._resizeAPI.getPosition()
    alert(`位置: (${position.x}, ${position.y})\n大小: ${size.width} × ${size.height}`)
  }
}
</script>

<style scoped>
.drag-resize-demo {
  padding: 20px;
  min-height: 100vh;
  background: #f5f5f5;
}

.demo-container {
  position: relative;
  min-height: 1000px;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.demo-section h2 {
  margin-top: 0;
  color: #333;
}

.draggable-resizable {
  position: absolute;
  border: 2px solid #007bff;
  border-radius: 8px;
  background: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  cursor: move;
}

.draggable-resizable.basic {
  left: 50px;
  top: 50px;
  width: 300px;
  height: 200px;
}

.draggable-resizable.with-header {
  left: 350px;
  top: 50px;
  width: 350px;
  height: 250px;
}

.draggable-resizable.api-demo {
  left: 50px;
  top: 600px;
  width: 300px;
  height: 200px;
}

.content {
  padding: 20px;
}

.content h3 {
  margin-top: 0;
  color: #007bff;
}

.content p {
  margin: 8px 0;
  color: #555;
  font-size: 14px;
}

.drag-header {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 12px 20px;
  border-radius: 6px 6px 0 0;
  cursor: move;
  user-select: none;
}

.drag-header h3 {
  margin: 0;
  font-size: 16px;
}

.window {
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.window-header {
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  user-select: none;
  border-radius: 6px 6px 0 0;
}

.window-content {
  padding: 12px;
}

.window-content p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #ff4757;
  background: #f1f2f6;
  border-radius: 50%;
}

.controls {
  margin: 20px 0;
}

.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn:hover {
  background: #0056b3;
}

/* 自定义手柄样式 */
:deep(.resize-handle) {
  transition: background-color 0.2s ease;
}

:deep(.resize-handle:hover) {
  background-color: rgba(0, 123, 255, 0.6) !important;
}
</style>
