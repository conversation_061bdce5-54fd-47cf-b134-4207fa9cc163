import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import cesium from "vite-plugin-cesium";
import VueSetupExtend from "vite-plugin-vue-setup-extend";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

export default defineConfig({
  base: "./",
  // 添加server配置
  server: {
    host: "0.0.0.0", // 允许通过本地IP访问
    port: 5173, // 可选，指定端口号
    open: true, // 可选，自动打开浏览器
    cors: true, // 可选，允许跨域
  },
  plugins: [
    vue(),
    cesium(),
    VueSetupExtend(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
  optimizeDeps: {
    include: ["schart.js"],
  },
  resolve: {
    alias: {
      "@": "/src",
      "~": "/src/assets",
      cesium: "cesium", // Cesium 别名
    },
  },
  build: {
    target: "es2015",
    rollupOptions: {
      output: {
        globals: {
          cesium: "Cesium",
        },
      },
    },
  },
  define: {
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: "true",
    CESIUM_BASE_URL: JSON.stringify("./cesium/"),
  },
});
